name: Deploy to Classic Kamael OVH Server
on:
  workflow_dispatch:
    inputs:
      maxdbcon:
        default: "3000"
        description: "GameServer MaxDbConnections (INT)" 
      gmmode:
        type: boolean
        default: true
        description: "GM login only?"
      minRamGS:
        default: "8"
        description: "GameServer start with min RAM (INT)"   
      maxRamGS:
        default: "32"
        description: "GameServer start with max RAM (INT)"
      maxClient:
        default: "0"
        description: "max client (INT, 0=unlimited)"     

jobs:
  stop_LSGS:
    runs-on: classic_kamael_OVH
    name: Stop GameServer, LoginServer
    steps:
    - name: stop LoginServer process
      run: pkill -f LoginServer
      continue-on-error: true
    - name: stop GameServer process
      run: pkill -f GameServer
      continue-on-error: true
      
  checkout:
    runs-on: classic_kamael_OVH
    needs: [stop_LSGS]
    name: Checkout SourceCode
    steps:
    - uses: actions/checkout@main
      with:
        token: ${{ secrets.GH_PAT }}
    - name: clean gameserver subData folder
      working-directory: ../build/game/data
      run: rm -rf scripts spawns
      
  build:
    runs-on: classic_kamael_OVH
    needs: [checkout]
    name: Build SourceCode
    steps:
    - name: Build with Ant
      run: ant
      
  config_GS:
    runs-on: classic_kamael_OVH
    needs: [build]
    name: Config GameServer
    env:
      GMMODE: ${{ github.event.inputs.gmmode }}
    steps:  
    - name: update connection string
      working-directory: ../build/game/config
      run: |
        sed -i "s/localhost\/.*?/localhost\/${{ secrets.classic_kamael_ovh_dbname }}?/g" Server.ini
        sed -i "s/Login =.*/Login = ${{ secrets.classic_kamael_ovh_dbuser }}/g" Server.ini
        sed -i "s/Password =.*/Password = ${{ secrets.classic_kamael_ovh_dbpass }}/g" Server.ini
        sed -i "s/MaximumDbConnections =.*/MaximumDbConnections = ${{ github.event.inputs.maxdbcon }}/g" Server.ini
    
    - name: Check login mode for gm only
      working-directory: ../build/game/config
      run: sed -i "s/ServerGMOnly.*/ServerGMOnly = $GMMODE/g" General.ini

    - name: schedule restart server
      working-directory: ../build/game/config
      run: |
        sed -i 's/ServerRestartScheduleEnabled = False/ServerRestartScheduleEnabled = True/g' Server.ini
        sed -i 's/ServerRestartSchedule = 08:00/ServerRestartSchedule = 18:00/g' Server.ini
        sed -i 's/ServerRestartDays = 4/ServerRestartDays = 1,2,3,4,5,6,7/g' Server.ini

    - name: update java config, min max RAM
      working-directory: ../build/game
      run: |
        sed -i "s/Xmx4g/Xmx${{ github.event.inputs.maxRamGS }}g/g" java.cfg
        sed -i "s/Xms2g/Xms${{ github.event.inputs.minRamGS }}g/g" java.cfg
    - name: LOG
      working-directory: ../build/game
      run: mkdir -p log >/dev/null 2>&1
      
    - name: chmod execute sh files
      working-directory: ../build/game
      run: chmod +x *.sh
    
  
  config_LS:
    runs-on: classic_kamael_OVH
    needs: [build]
    name: Config LoginServer
    steps:  
    - name: update connection string
      working-directory: ../build/login/config
      run: |
        sed -i "s/localhost\/.*?/localhost\/${{ secrets.classic_kamael_ovh_dbname }}?/g" LoginServer.ini
        sed -i "s/Login =.*/Login = ${{ secrets.classic_kamael_ovh_dbuser }}/g" LoginServer.ini
        sed -i "s/Password =.*/Password = ${{ secrets.classic_kamael_ovh_dbpass }}/g" LoginServer.ini
        sed -i "s/LoginRestartSchedule =.*/LoginRestartSchedule = True/g" LoginServer.ini
        sed -i "s/LoginRestartTime =.*/LoginRestartTime = 12/g" LoginServer.ini
    - name: create LOG folder
      working-directory: ../build/login
      run: mkdir -p log >/dev/null 2>&1
    - name: chmod execute sh files
      working-directory: ../build/login
      run: chmod +x *.sh
    - name: disable auto craete account in Live env
      working-directory: ../build/login/config
      run: sed -i "s/AutoCreateAccounts = True/AutoCreateAccounts = True/g" LoginServer.ini
      

  start_LS:
    runs-on: classic_kamael_OVH
    needs: [config_LS]
    steps:
    - name: start LS 
      working-directory: ../build/login
      run: RUNNER_TRACKING_ID="" && sh LoginServer.sh
      
      
      
      
  start_GS:
    runs-on: classic_kamael_OVH
    needs: [config_GS]
    steps:
    - name: start GS 
      working-directory: ../build/game
      run: RUNNER_TRACKING_ID="" && sh GameServer.sh
      