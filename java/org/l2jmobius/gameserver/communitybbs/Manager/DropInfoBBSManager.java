package org.l2jmobius.gameserver.communitybbs.Manager;

import java.util.List;
import java.util.StringTokenizer;

import org.l2jmobius.Config;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoFunctions;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoHandler;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.DropInfoHolder;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.ImagesCache;
import org.l2jmobius.gameserver.data.SpawnTable;
import org.l2jmobius.gameserver.data.xml.ItemData;
import org.l2jmobius.gameserver.data.xml.NpcData;
import org.l2jmobius.gameserver.enums.ChatType;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.model.Spawn;
import org.l2jmobius.gameserver.model.actor.Npc;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.templates.NpcTemplate;
import org.l2jmobius.gameserver.model.item.ItemTemplate;
import org.l2jmobius.gameserver.model.zone.ZoneId;
import org.l2jmobius.gameserver.network.serverpackets.CreatureSay;
import org.l2jmobius.gameserver.network.serverpackets.RadarControl;
import org.l2jmobius.gameserver.util.Util;

public class DropInfoBBSManager extends BaseBBSManager
{
	@Override
	public void parsecmd(String command, Player player)
	{
		if (!Config.ENABLE_DROP_CALCULATOR)
		{
			player.sendMessage("Drop Calculator is disabled by admin.");
			return;
		}
		StringTokenizer st = new StringTokenizer(command, "_");
		st.nextToken();
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropCalcMain.htm");
		if (html == null)
		{
			html = "<html><body><br><br><center>404 :File not found: 'data/html/CommunityBoard/Custom/DropCalculator/bbs_dropCalcMain.htm'</center></body></html>";
		}
		if (command.equals("_bbssearchdropCalc"))
		{
			showMainPage(player);
			return;
		}
		else if (command.equals("_bbssearchdropItemsByName_"))
		{
			String itemName = command.substring(command.indexOf("_") + 1).trim();
			if (itemName.isEmpty())
			{
				player.sendMessage("Invalid item name.");
				showMainPage(player);
				return;
			}
			int itemsPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropItemsByNamePage(player, itemName, itemsPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonstersByItem_"))
		{
			int itemId = Integer.parseInt(st.nextToken());
			int monstersPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropMonstersByItem(player, itemId, monstersPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonsterDetailsByItem_"))
		{
			int monsterId = Integer.parseInt(st.nextToken());
			html = showdropMonsterDetailsByItem(player, monsterId);
			CommunityBoardHandler.separateAndSend(html, player);
			if (st.hasMoreTokens())
			{
				manageButton(player, Integer.parseInt(st.nextToken()), monsterId);
			}
			return;
		}
		else if (command.equals("_bbssearchdropMonstersByName_"))
		{
			if (!st.hasMoreTokens())
			{
				showMainPage(player);
				return;
			}
			String monsterName = st.nextToken().trim();
			int monsterPage = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 1;
			int sortMethod = st.hasMoreTokens() ? Integer.parseInt(st.nextToken()) : 0;
			html = showDropMonstersByName(player, monsterName, monsterPage, sortMethod);
		}
		else if (command.equals("_bbssearchdropMonsterDetailsByName_"))
		{
			int chosenMobId = Integer.parseInt(st.nextToken());
			html = showDropMonsterDetailsByName(player, chosenMobId);
			CommunityBoardHandler.separateAndSend(html, player);
			if (st.hasMoreTokens())
			{
				manageButton(player, Integer.parseInt(st.nextToken()), chosenMobId);
			}
			return;
		}
		else if (command.equals("_bbssearchNpcDropList"))
		{
			player.getVariables().set("DCDropType", command.split("_")[2]);
			DropInfoFunctions.showNpcDropList(player, command.split("_")[2], Integer.parseInt(command.split("_")[3]), Integer.parseInt(command.split("_")[4]));
			return;
		}
		else if (command.equals("_bbssearchShowSkills"))
		{
			DropInfoFunctions.showNpcSkillList(player, Integer.parseInt(command.split("_")[2]), Integer.parseInt(command.split("_")[3]));
			return;
		}
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	public void showMainPage(Player player)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropCalcMain.htm");
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	public String showDropMonstersByName(Player player, String monsterName, int page, int sort)
	{
		player.getVariables().set("DCMonsterSort", sort);
		player.getVariables().set("DCMonsterName", monsterName);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonstersByName.htm");
		return replaceMonstersByName(html, monsterName, page, sort);
	}
	
	private static String replaceMonstersByName(String html, String monsterName, int page, int sort)
	{
		String newHtml = html;
		List<NpcTemplate> npcTemplates = DropInfoFunctions.getNpcsContainingString(monsterName);
		npcTemplates = DropInfoFunctions.sortMonsters(npcTemplates, sort);
		int npcIndex = 0;
		for (int i = 0; i < 12; i++)
		{
			npcIndex = i + ((page - 1) * 12);
			NpcTemplate npc = npcTemplates.size() > npcIndex ? npcTemplates.get(npcIndex) : null;
			newHtml = newHtml.replace("<?name_" + i + "?>", npc != null ? DropInfoFunctions.getName(npc.getName()) : "...");
			newHtml = newHtml.replace("<?drop_" + i + "?>", npc != null ? String.valueOf(DropInfoFunctions.getDropsCount(npc, false)) : "...");
			newHtml = newHtml.replace("<?spoil_" + i + "?>", npc != null ? String.valueOf(DropInfoFunctions.getDropsCount(npc, true)) : "...");
			newHtml = newHtml.replace("<?bp_" + i + "?>", npc != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByName_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		newHtml = newHtml.replace("<?previous?>", page > 1 ? "<button action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("<?next?>", npcTemplates.size() > (npcIndex + 1) ? "<button action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		newHtml = newHtml.replace("<?search?>", monsterName);
		newHtml = newHtml.replace("<?size?>", Util.formatAdena(npcTemplates.size()));
		newHtml = newHtml.replace("<?page?>", String.valueOf(page));
		newHtml = newHtml.replace("<?monsterName?>", sort == 0 ? "<font color=\"bbbbbb\">Monster Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Monster Name</font></a>");
		newHtml = newHtml.replace("<?droppingItems?>", sort == 1 ? "<font color=\"bbbbbb\">Dropping Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Dropping Items</font></a>");
		newHtml = newHtml.replace("<?spoilingItems?>", sort == 2 ? "<font color=\"bbbbbb\">Spoiling Items</font>" : "<a action=\"bypass _bbssearchdropMonstersByName_" + monsterName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Spoiling Items</font></a>");
		return newHtml;
	}
	
	public String showDropItemsByNamePage(Player player, String itemName, int page, int sort)
	{
		player.getVariables().set("DCItemSort", sort);
		player.getVariables().set("DCItemName", itemName);
		player.getVariables().set("DCItemsPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropItemsByName.htm");
		return replaceItemsByNamePage(html, itemName, page, sort);
	}
	
	private String replaceItemsByNamePage(String html, String itemName, int page, int sort)
	{
		String newHtml = html;
		List<ItemTemplate> itemsByName = DropInfoFunctions.getItemsByNameContainingString(itemName);
		itemsByName = DropInfoFunctions.sortItemTemplates(itemsByName, sort);
		int itemIndex = 0;
		for (int i = 0; i < 12; i++)
		{
			itemIndex = i + ((page - 1) * 12);
			ItemTemplate item = itemsByName.size() > itemIndex ? itemsByName.get(itemIndex) : null;
			newHtml = newHtml.replace("<?name_" + i + "?>", item != null ? DropInfoFunctions.getName(item.getName()) : "...");
			newHtml = newHtml.replace("<?drop_" + i + "?>", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), false)) : "...");
			newHtml = newHtml.replace("<?spoil_" + i + "?>", item != null ? String.valueOf(DropInfoFunctions.getDroplistsCountByItemId(item.getId(), true)) : "...");
			newHtml = newHtml.replace("<?bp_" + i + "?>", item != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonstersByItem_" + item.getId() + "_1\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		newHtml = newHtml.replace("<?previous?>", page > 1 ? "<button action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("<?next?>", itemsByName.size() > (itemIndex + 1) ? "<button action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		newHtml = newHtml.replace("<?search?>", itemName);
		newHtml = newHtml.replace("<?size?>", Util.formatAdena(itemsByName.size()));
		newHtml = newHtml.replace("<?page?>", String.valueOf(page));
		newHtml = newHtml.replace("<?itemName?>", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("<?dropLists?>", sort == 1 ? "<font color=\"bbbbbb\">Number of Drop Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Number of Drop Lists</font></a>");
		newHtml = newHtml.replace("<?spoilLists?>", sort == 2 ? "<font color=\"bbbbbb\">Number of Spoil Lists</font>" : "<a action=\"bypass _bbssearchdropItemsByName_" + itemName + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Number of Spoil Lists</font></a>");
		return newHtml;
	}
	
	public String showDropMonstersByItem(Player player, int itemId, int page, int sort)
	{
		player.getVariables().set("DCMonster2Sort", sort);
		player.getVariables().set("DCItemId", itemId);
		player.getVariables().set("DCMonstersPage", page);
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonstersByItem.htm");
		return replaceMonstersByItemPage(player, html, itemId, page, sort);
	}
	
	private String replaceMonstersByItemPage(Player player, String html, int itemId, int page, int sort)
	{
		String newHtml = html;
		List<DropInfoHolder> templates = DropInfoHandler.getInstance().getDrop(itemId);
		templates = DropInfoFunctions.sortMonsters2(templates, sort);
		int npcIndex = 0;
		for (int i = 0; i < 12; i++)
		{
			npcIndex = i + ((page - 1) * 12);
			DropInfoHolder drops = templates.size() > npcIndex ? templates.get(npcIndex) : null;
			NpcTemplate npc = templates.size() > npcIndex ? NpcData.getInstance().getTemplate(templates.get(npcIndex).getNpcId()) : null;
			newHtml = newHtml.replace("<?name_" + i + "?>", npc != null ? DropInfoFunctions.getName(npc.getName()) : "...");
			newHtml = newHtml.replace("<?level_" + i + "?>", npc != null ? String.valueOf(npc.getLevel()) : "...");
			newHtml = newHtml.replace("<?type_" + i + "?>", (npc != null) && (drops != null) ? drops.isSweep() ? "Spoil" : "Drop" : "...");
			newHtml = newHtml.replace("<?count_" + i + "?>", (npc != null) && (drops != null) ? DropInfoFunctions.getMinMaxDropCounts(npc, itemId, drops.isSweep()) : "...");
			newHtml = newHtml.replace("<?chance_" + i + "?>", (npc != null) && (drops != null) ? DropInfoFunctions.getDropChance(npc, itemId, drops.isSweep(), player) : "...");
			newHtml = newHtml.replace("<?bp_" + i + "?>", npc != null ? "<button value=\"show\" action=\"bypass _bbssearchdropMonsterDetailsByItem_" + npc.getId() + "\" width=40 height=12 back=\"L2UI_CT1.ListCTRL_DF_Title_Down\" fore=\"L2UI_CT1.ListCTRL_DF_Title\">" : "...");
		}
		newHtml = newHtml.replace("<?previous?>", page > 1 ? "<button action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page - 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_prev_down\" fore=\"L2UI_CH3.shortcut_prev\">" : "<br>");
		newHtml = newHtml.replace("<?next?>", templates.size() > (npcIndex + 1) ? "<button action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + (page + 1) + "_" + sort + "\" width=16 height=16 back=\"L2UI_CH3.shortcut_next_down\" fore=\"L2UI_CH3.shortcut_next\">" : "<br>");
		newHtml = newHtml.replace("<?search?>", player.getVariables().getString("DCItemName", ItemData.getInstance().getTemplate(itemId).getName()));
		newHtml = newHtml.replace("<?item?>", ItemData.getInstance().getTemplate(itemId).getName());
		newHtml = newHtml.replace("<?size?>", Util.formatAdena(templates.size()));
		newHtml = newHtml.replace("<?back?>", String.valueOf(player.getVariables().getInt("DCItemsPage", 1)));
		newHtml = newHtml.replace("<?page?>", String.valueOf(page));
		newHtml = newHtml.replace("<?monsterName?>", sort == 0 ? "<font color=\"bbbbbb\">Name</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 0 + "\"><font color=\"bbbbbb\">Name</font></a>");
		newHtml = newHtml.replace("<?level?>", sort == 1 ? "<font color=\"bbbbbb\">Level</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 1 + "\"><font color=\"bbbbbb\">Level</font></a>");
		newHtml = newHtml.replace("<?chance?>", sort == 2 ? "<font color=\"bbbbbb\">Chance</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 2 + "\"><font color=\"bbbbbb\">Chance</font></a>");
		newHtml = newHtml.replace("<?type?>", sort == 3 ? "<font color=\"bbbbbb\">Type</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 3 + "\"><font color=\"bbbbbb\">Type</font></a>");
		newHtml = newHtml.replace("<?count?>", sort == 4 ? "<font color=\"bbbbbb\">Count [Min...Max]</font>" : "<a action=\"bypass _bbssearchdropMonstersByItem_" + itemId + "_" + page + "_" + 4 + "\"><font color=\"bbbbbb\">Count [Min...Max]</font></a>");
		newHtml = newHtml.replace("<?sort?>", String.valueOf(player.getVariables().getInt("DCItemSort", 0)));
		return newHtml;
	}
	
	public String showdropMonsterDetailsByItem(Player player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonsterDetailsByItem.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	public String showDropMonsterDetailsByName(Player player, int monsterId)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/DropCalculator/bbs_dropMonsterDetailsByName.htm");
		return replaceMonsterDetails(player, html, monsterId);
	}
	
	private String replaceMonsterDetails(Player player, String html, int monsterId)
	{
		String newHtml = html;
		int itemId = player.getVariables().getInt("DCItemId", -1);
		NpcTemplate template = NpcData.getInstance().getTemplate(monsterId);
		ItemTemplate item = itemId > -1 ? ItemData.getInstance().getTemplate(itemId) : null;
		newHtml = newHtml.replace("<?name?>", String.valueOf(player.getVariables().getString("DCMonsterName")));
		newHtml = newHtml.replace("<?monster_name?>", template.getName());
		newHtml = newHtml.replace("<?item?>", item != null ? item.getName() : "...");
		newHtml = newHtml.replace("<?item_id?>", item != null ? String.valueOf(item.getId()) : "...");
		newHtml = newHtml.replace("<?back?>", String.valueOf(player.getVariables().getString("DCMonstersPage")));
		newHtml = newHtml.replace("<?monster?>", String.valueOf(monsterId));
		newHtml = newHtml.replace("<?level?>", String.valueOf(template.getLevel()));
		newHtml = newHtml.replace("<?aggro?>", template.isAggressive() ? "TRUE" : "FALSE");
		newHtml = newHtml.replace("<?hp?>", Util.formatAdena((int) template.getBaseHpMax()));
		newHtml = newHtml.replace("<?mp?>", Util.formatAdena((int) template.getBaseMpMax()));
		newHtml = newHtml.replace("<?drop?>", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), false, player) : "...");
		newHtml = newHtml.replace("<?spoil?>", item != null ? DropInfoFunctions.getDropChance(template, item.getId(), true, player) : "...");
		newHtml = newHtml.replace("<?droping?>", String.valueOf(DropInfoFunctions.getDropsCount(template, false)));
		newHtml = newHtml.replace("<?spoiling?>", String.valueOf(DropInfoFunctions.getDropsCount(template, true)));
		newHtml = newHtml.replace("<?sort?>", String.valueOf(player.getVariables().getString("DCMonsterSort")));
		newHtml = newHtml.replace("<?sort2?>", String.valueOf(player.getVariables().getString("DCMonster2Sort")));
		newHtml = newHtml.replace("<?image?>", "Crest.crest_" + String.valueOf(Config.SERVER_ID) + "_" + String.valueOf(monsterId));
		ImagesCache.getInstance().sendImageToPlayer(player, monsterId);
		return newHtml;
	}
	
	public void manageButton(Player player, int buttonId, int monsterId)
	{
		switch (buttonId)
		{
			case 1:
				player.sendPacket(new RadarControl(2, 2, 0, 0, 0));
				break;
			case 2:// Show Drops
				DropInfoFunctions.showNpcDropList(player, "DEATH", monsterId, 1);
				break;
			case 3:// Teleport To Monster
				if (Config.ENABLE_TELEPORT_FUNCTION)
				{
					if (Config.ALLOW_TELEPORT_FROM_PEACE_ZONE_ONLY && !player.isInsideZone(ZoneId.PEACE))
					{
						player.sendMessage("Teleport is only allowed from peace zones only.");
						return;
					}
					Npc aliveInstance = DropInfoFunctions.getAliveNpc(monsterId);
					if ((aliveInstance != null) && !Config.RESTRICTED_TELEPORT_IDS.contains(aliveInstance.getId()))
					{
						if (!Config.ALLOW_FREE_TELEPORT && !player.destroyItemByItemId("DropCalc", Config.TELEPORT_PRICE[0], Config.TELEPORT_PRICE[1], player, true))
						{
							player.sendMessage("Incorrect item count.");
							return;
						}
						player.teleToLocation(aliveInstance.getLocation());
					}
					else
					{
						player.sendMessage("Monster isn't alive or teleport is not allowed.");
					}
				}
				else
				{
					player.sendMessage("Teleport function is disabled.");
				}
				break;
			case 4:// Show Monster on Map
				player.sendPacket(new CreatureSay(player, ChatType.PARTYROOM_COMMANDER, "Info", "Open Map to see Locations"));
				for (Spawn loc : SpawnTable.getInstance().getSpawns(monsterId))
				{
					player.sendPacket(new RadarControl(0, 1, loc.getX(), loc.getY(), loc.getZ()));
				}
				break;
			case 5: // Npc stats
				DropInfoFunctions.showStats(player, monsterId);
				break;
		}
	}
	
	public static DropInfoBBSManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final DropInfoBBSManager INSTANCE = new DropInfoBBSManager();
	}
	
	@Override
	public void parsewrite(String ar1, String ar2, String ar3, String ar4, String ar5, Player player)
	{
		// TODO Auto-generated method stub
	}
}
