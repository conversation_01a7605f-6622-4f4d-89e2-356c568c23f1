package org.l2jmobius.gameserver.communitybbs.Manager;

import org.l2jmobius.Config;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.communitybbs.SunriseBoards.PartyMatchingBoard;
import org.l2jmobius.gameserver.enums.PartyDistributionType;
import org.l2jmobius.gameserver.enums.PlayerCondOverride;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.model.BlockList;
import org.l2jmobius.gameserver.model.Party;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.request.PartyRequest;
import org.l2jmobius.gameserver.network.SystemMessageId;
import org.l2jmobius.gameserver.network.serverpackets.AskJoinParty;
import org.l2jmobius.gameserver.network.serverpackets.ShowBoard;
import org.l2jmobius.gameserver.network.serverpackets.SystemMessage;

public class PartyMatchingBBSManager extends BaseBBSManager
{
	public String _BBSCommand = "_maillist_0_1_0_";
	
	@Override
	public void parsecmd(String command, Player activeChar)
	{
		if (command.startsWith("_bbspartymatchinginvite"))
		{
			String targetName = command.substring(24);
			Player receiver = World.getInstance().getPlayer(targetName);
			SystemMessage sm;
			if (receiver == null)
			{
				activeChar.sendPacket(SystemMessageId.YOU_MUST_FIRST_SELECT_A_USER_TO_INVITE_TO_YOUR_PARTY);
			}
			else if ((receiver.getClient() == null) || receiver.getClient().isDetached())
			{
				activeChar.sendMessage("Player is in offline mode.");
			}
			else if (!activeChar.canOverrideCond(PlayerCondOverride.SEE_ALL_PLAYERS) && receiver.isInvisible())
			{
				activeChar.sendPacket(SystemMessageId.THAT_IS_AN_INCORRECT_TARGET);
			}
			else if (receiver.isInParty())
			{
				sm = new SystemMessage(SystemMessageId.C1_IS_A_MEMBER_OF_ANOTHER_PARTY_AND_CANNOT_BE_INVITED);
				sm.addString(receiver.getName());
				activeChar.sendPacket(sm);
			}
			else if (BlockList.isBlocked(receiver, activeChar))
			{
				sm = new SystemMessage(SystemMessageId.C1_HAS_PLACED_YOU_ON_HIS_HER_IGNORE_LIST);
				sm.addPcName(receiver);
				activeChar.sendPacket(sm);
			}
			else if (receiver == activeChar)
			{
				activeChar.sendPacket(SystemMessageId.YOU_HAVE_INVITED_THE_WRONG_TARGET);
			}
			else if (receiver.isCursedWeaponEquipped() || activeChar.isCursedWeaponEquipped())
			{
				receiver.sendPacket(SystemMessageId.INVALID_TARGET);
			}
			// else if (receiver.isInJail() || activeChar.isInJail())
			// {
			// activeChar.sendMessage("You cannot invite a player while is in Jail.");
			// }
			else if (receiver.isInOlympiadMode() || activeChar.isInOlympiadMode())
			{
				if ((receiver.isInOlympiadMode() != activeChar.isInOlympiadMode()) || (receiver.getOlympiadGameId() != activeChar.getOlympiadGameId()) || (receiver.getOlympiadSide() != activeChar.getOlympiadSide()))
				{
					activeChar.sendPacket(SystemMessageId.A_USER_CURRENTLY_PARTICIPATING_IN_THE_OLYMPIAD_CANNOT_SEND_PARTY_AND_FRIEND_INVITATIONS);
					return;
				}
			}
			else
			{
				sm = new SystemMessage(SystemMessageId.C1_HAS_BEEN_INVITED_TO_THE_PARTY);
				sm.addString(receiver.getName());
				activeChar.sendPacket(sm);
				if (!activeChar.isInParty())
				{
					createNewParty(receiver, activeChar);
				}
				else
				{
					addTargetToParty(receiver, activeChar);
				}
			}
			sendHtm(activeChar, "main", command);
		}
		else if (command.startsWith("_bbspartymatchinglist"))
		{
			String[] value = command.split(" ");
			String type = value[1];
			if (type.equals("on"))
			{
				if (activeChar.isInParty())
				{
					activeChar.sendMessage("You can't use this while you're in party!");
				}
				activeChar.getVariables().set("partyMatch", true);
				activeChar.sendMessage("You've entered the party matching list.");
			}
			else if (type.equals("off"))
			{
				if (activeChar.isInParty())
				{
					activeChar.sendMessage("You can't use this while you're in party!");
					return;
				}
				activeChar.getVariables().remove("partyMatch");
				activeChar.sendMessage("You've left the party matching list.");
			}
			sendHtm(activeChar, "main", command);
		}
		else if (command.startsWith("_bbspartymatchingrefresh") || command.equals("_bbspartymatching") || command.equals(_BBSCommand))
		{
			sendHtm(activeChar, "main", command);
		}
		else
		{
			ShowBoard sb = new ShowBoard("<html><body><br><br><center>The command: " + command + " is not implemented yet.</center><br><br></body></html>", "101");
			activeChar.sendPacket(sb);
			activeChar.sendPacket(new ShowBoard(null, "102"));
			activeChar.sendPacket(new ShowBoard(null, "103"));
		}
	}
	
	private void sendHtm(Player activeChar, String string, String command)
	{
		String content = HtmCache.getInstance().getHtm(activeChar, "data/html/CommunityBoard/Custom/partyMatching/" + string + ".htm");
		if (content == null)
		{
			content = "<html><body><br><br><center>404 :File not found: 'data/html/CommunityBoard/partyMatching/" + string + ".htm'</center></body></html>";
		}
		content = content.replace("%partyMatchingMembers%", PartyMatchingBoard.getInstance().getList());
		CommunityBoardHandler.separateAndSend(content, activeChar);
	}
	
	private void addTargetToParty(Player receiver, Player requestor)
	{
		final Party party = requestor.getParty();
		if (!party.isLeader(requestor))
		{
			requestor.sendPacket(SystemMessageId.ONLY_THE_LEADER_CAN_GIVE_OUT_INVITATIONS);
			return;
		}
		else if (party.getMemberCount() >= Config.ALT_PARTY_MAX_MEMBERS)
		{
			requestor.sendPacket(SystemMessageId.THE_PARTY_IS_FULL);
			return;
		}
		else if (party.getPendingInvitation() && !party.isInvitationRequestExpired())
		{
			requestor.sendPacket(SystemMessageId.WAITING_FOR_ANOTHER_REPLY);
			return;
		}
		else if (!receiver.hasRequest(PartyRequest.class))
		{
			final PartyRequest request = new PartyRequest(requestor, receiver, party);
			request.scheduleTimeout(30 * 1000);
			requestor.addRequest(request);
			receiver.addRequest(request);
			receiver.sendPacket(new AskJoinParty(requestor.getName(), party.getDistributionType()));
			party.setPendingInvitation(true);
			receiver.getVariables().set("partyMatch", false);
		}
		else
		{
			SystemMessage sm = new SystemMessage(SystemMessageId.C1_IS_ON_ANOTHER_TASK_PLEASE_TRY_AGAIN_LATER);
			sm.addString(receiver.getName());
			requestor.sendPacket(sm);
		}
	}
	
	private void createNewParty(Player receiver, Player requestor)
	{
		if (!receiver.hasRequest(PartyRequest.class))
		{
			final Party party = new Party(requestor, PartyDistributionType.RANDOM);
			party.setPendingInvitation(true);
			final PartyRequest request = new PartyRequest(requestor, receiver, party);
			request.scheduleTimeout(30 * 1000);
			requestor.addRequest(request);
			receiver.addRequest(request);
			receiver.sendPacket(new AskJoinParty(requestor.getName(), PartyDistributionType.RANDOM));
			receiver.getVariables().set("partyMatch", false);
		}
		else
		{
			requestor.sendPacket(SystemMessageId.WAITING_FOR_ANOTHER_REPLY);
		}
	}
	
	@Override
	public void parsewrite(String ar1, String ar2, String ar3, String ar4, String ar5, Player player)
	{
		// TODO Auto-generated method stub
	}
	
	public static PartyMatchingBBSManager getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final PartyMatchingBBSManager _instance = new PartyMatchingBBSManager();
	}
}
