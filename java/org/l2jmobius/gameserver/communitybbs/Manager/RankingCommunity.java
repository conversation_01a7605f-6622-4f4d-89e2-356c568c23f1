/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.communitybbs.Manager;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.data.sql.ClanTable;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.handler.IParseBoardHandler;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.clan.Clan;
import org.l2jmobius.gameserver.util.Util;

/**
 * <AUTHOR>
 */
public class RankingCommunity implements IParseBoardHandler
{
	private static final Logger		_log		= Logger.getLogger(RankingCommunity.class.getName());
	private static final String[]	COMMANDS	=
	{
		"_bbsranking"
	};
	
	private static class RankingManager
	{
		private final String[]	RankingPvPName		= new String[10];
		private final String[]	RankingPvPClan		= new String[10];
		private final int[]		RankingPvPClass		= new int[10];
		private final int[]		RankingPvPOn		= new int[10];
		private final int[]		RankingPvP			= new int[10];
		private final String[]	RankingPkName		= new String[10];
		private final String[]	RankingPkClan		= new String[10];
		private final int[]		RankingPkClass		= new int[10];
		private final int[]		RankingPkOn			= new int[10];
		private final int[]		RankingPk			= new int[10];
		private final String[]	RankingRaidName		= new String[10];
		private final String[]	RankingRaidClan		= new String[10];
		private final int[]		RankingRaidClass	= new int[10];
		private final int[]		RankingRaidOn		= new int[10];
		private final int[]		RankingRaid			= new int[10];
		private final String[]	RankingFameName		= new String[10];
		private final String[]	RankingFameClan		= new String[10];
		private final int[]		RankingFameClass	= new int[10];
		private final int[]		RankingFameOn		= new int[10];
		private final int[]		RankingFame			= new int[10];
		private final String[]	RankingEXPName		= new String[10];
		private final String[]	RankingEXPClan		= new String[10];
		private final int[]		RankingEXPClass		= new int[10];
		private final int[]		RankingEXPOn		= new int[10];
		private final int[]		RankingEXP			= new int[10];
		// private final String[] RankingInstanceSoloName = new String[10];
		// private final String[] RankingInstanceSoloClan = new String[10];
		// private final int[] RankingInstanceSoloClass = new int[10];
		// private final int[] RankingInstanceSoloOn = new int[10];
		// private final int[] RankingInstanceSolo = new int[10];
		//
		// private final String[] RankingInstancePartyName = new String[10];
		// private final String[] RankingInstancePartyClan = new String[10];
		// private final int[] RankingInstancePartyClass = new int[10];
		// private final int[] RankingInstancePartyOn = new int[10];
		// private final int[] RankingInstanceParty = new int[10];
		private final String[]	RankingAdenaName	= new String[10];
		private final String[]	RankingAdenaClan	= new String[10];
		private final int[]		RankingAdenaClass	= new int[10];
		private final int[]		RankingAdenaOn		= new int[10];
		private final long[]	RankingAdena		= new long[10];
	}
	
	static RankingManager	RankingManagerStats	= new RankingManager();
	private long			update				= System.currentTimeMillis() / 1000;
	private final int		time_update			= 60;
	
	@Override
	public boolean parseCommunityBoardCommand(String command, Player player)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/menu.htm");
		CommunityBoardHandler.separateAndSend(html, player);
		// Checking if all required images were sent to the player, if not - not allowing to pass
		// if (!AutoImageSenderManager.wereAllImagesSent(player))
		// {
		// player.sendPacket(new CreatureSay(player, ChatType.CRITICAL_ANNOUNCE, "CB", "Community wasn't loaded yet, try again in few seconds."));
		// return false;
		// }
		// player.setSessionVar("add_fav", null);
		if ((update + (time_update * 60)) < (System.currentTimeMillis() / 1000))
		{
			selectRankingPK();
			selectRankingPVP();
			selectRankingRK();
			selectRankingFame();
			selectRankingExp();
			// selectRankingCIS();
			// selectRankingCIP();
			selectRankingAdena();
			update = System.currentTimeMillis() / 1000;
			_log.info("Ranking in the commynity board has been updated.");
		}
		if (command.equals("_bbsranking") || command.equals("_bbsranking:pk"))
		{
			show(player, 1);
		}
		else if (command.equals("_bbsranking:pvp"))
		{
			show(player, 2);
		}
		else if (command.equals("_bbsranking:rk"))
		{
			show(player, 3);
		}
		else if (command.equals("_bbsranking:cis"))
		{
			show(player, 4);
		}
		else if (command.equals("_bbsranking:cip"))
		{
			show(player, 5);
		}
		else if (command.equals("_bbsranking:adena"))
		{
			show(player, 6);
		}
		else if (command.equals("_bbsranking:fame"))
		{
			show(player, 7);
		}
		else if (command.equals("_bbsranking:exp"))
		{
			show(player, 8);
		}
		return true;
	}
	
	private void show(Player player, int page)
	{
		int number = 0;
		String html = null;
		if (page == 1)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/pk.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingPkName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingPkName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingPkClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingPkClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingPkClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingPkOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Integer.toString(RankingManagerStats.RankingPk[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		else if (page == 2)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/pvp.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingPvPName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingPvPName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingPvPClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingPvPClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingPvPClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingPvPOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Integer.toString(RankingManagerStats.RankingPvP[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		else if (page == 3)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/rk.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingRaidName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingRaidName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingRaidClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingRaidClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingRaidClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingRaidOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Integer.toString(RankingManagerStats.RankingRaid[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		// else if (page == 4)
		// {
		// html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/cis.htm");
		// while (number < 10)
		// {
		// if (RankingManagerStats.RankingInstanceSoloName[number] != null)
		// {
		// html = html.replace("%name_" + number + "%", RankingManagerStats.RankingInstanceSoloName[number]);
		// html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingInstanceSoloClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingInstanceSoloClan[number]);
		// html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingInstanceSoloClass[number]));
		// html = html.replace("%on_" + number + "%", RankingManagerStats.RankingInstanceSoloOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
		// html = html.replace("%count_" + number + "%", Integer.toString(RankingManagerStats.RankingInstanceSolo[number]));
		// }
		// else
		// {
		// html = html.replace("%name_" + number + "%", "...");
		// html = html.replace("%clan_" + number + "%", "...");
		// html = html.replace("%class_" + number + "%", "...");
		// html = html.replace("%on_" + number + "%", "...");
		// html = html.replace("%count_" + number + "%", "...");
		// }
		// number++;
		// }
		// }
		// else if (page == 5)
		// {
		// html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/cip.htm");
		// while (number < 10)
		// {
		// if (RankingManagerStats.RankingInstancePartyName[number] != null)
		// {
		// html = html.replace("%name_" + number + "%", RankingManagerStats.RankingInstancePartyName[number]);
		// html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingInstancePartyClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingInstancePartyClan[number]);
		// html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingInstancePartyClass[number]));
		// html = html.replace("%on_" + number + "%", RankingManagerStats.RankingInstancePartyOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
		// html = html.replace("%count_" + number + "%", Integer.toString(RankingManagerStats.RankingInstanceParty[number]));
		// }
		// else
		// {
		// html = html.replace("%name_" + number + "%", "...");
		// html = html.replace("%clan_" + number + "%", "...");
		// html = html.replace("%class_" + number + "%", "...");
		// html = html.replace("%on_" + number + "%", "...");
		// html = html.replace("%count_" + number + "%", "...");
		// }
		// number++;
		// }
		// }
		else if (page == 6)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/adena.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingAdenaName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingAdenaName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingAdenaClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingAdenaClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingAdenaClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingAdenaOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Long.toString(RankingManagerStats.RankingAdena[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		else if (page == 7)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/fame.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingFameName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingFameName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingFameClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingFameClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingFameClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingFameOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Long.toString(RankingManagerStats.RankingFame[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		else if (page == 8)
		{
			html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/exp.htm");
			while (number < 10)
			{
				if (RankingManagerStats.RankingEXPName[number] != null)
				{
					html = html.replace("%name_" + number + "%", RankingManagerStats.RankingEXPName[number]);
					html = html.replace("%clan_" + number + "%", RankingManagerStats.RankingEXPClan[number] == null ? "<font color=\"B59A75\">No Clan</font>" : RankingManagerStats.RankingEXPClan[number]);
					html = html.replace("%class_" + number + "%", Util.getFullClassName(RankingManagerStats.RankingEXPClass[number]));
					html = html.replace("%on_" + number + "%", RankingManagerStats.RankingEXPOn[number] == 1 ? "<font color=\"66FF33\">Yes</font>" : "<font color=\"B59A75\">No</font>");
					html = html.replace("%count_" + number + "%", Long.toString(RankingManagerStats.RankingEXP[number]));
				}
				else
				{
					html = html.replace("%name_" + number + "%", "...");
					html = html.replace("%clan_" + number + "%", "...");
					html = html.replace("%class_" + number + "%", "...");
					html = html.replace("%on_" + number + "%", "...");
					html = html.replace("%count_" + number + "%", "...");
				}
				number++;
			}
		}
		else
		{
			_log.warning("Unknown page: " + page + " - " + player.getName());
			return;
		}
		html = html.replace("%update%", String.valueOf(time_update));
		html = html.replace("%last_update%", String.valueOf(time(update)));
		html = html.replace("%ranking_menu%", HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/ranking/menu.htm"));
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	private static final DateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");
	
	private static String time(long time)
	{
		return TIME_FORMAT.format(new Date(time * 1000));
	}
	
	private void selectRankingPVP()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, pvpkills FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY pvpkills DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingPvPName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingPvPClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingPvPClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingPvPOn[number] = rset.getInt("online");
					RankingManagerStats.RankingPvP[number] = rset.getInt("pvpkills");
				}
				else
				{
					RankingManagerStats.RankingPvPName[number] = null;
					RankingManagerStats.RankingPvPClan[number] = null;
					RankingManagerStats.RankingPvPClass[number] = 0;
					RankingManagerStats.RankingPvPOn[number] = 0;
					RankingManagerStats.RankingPvP[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return;
	}
	
	private void selectRankingFame()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, fame FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY fame DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingFameName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingFameClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingFameClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingFameOn[number] = rset.getInt("online");
					RankingManagerStats.RankingFame[number] = rset.getInt("fame");
				}
				else
				{
					RankingManagerStats.RankingFameName[number] = null;
					RankingManagerStats.RankingFameClan[number] = null;
					RankingManagerStats.RankingFameClass[number] = 0;
					RankingManagerStats.RankingFameOn[number] = 0;
					RankingManagerStats.RankingFame[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return;
	}
	
	private void selectRankingExp()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, exp FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY exp DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingEXPName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingEXPClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingEXPClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingEXPOn[number] = rset.getInt("online");
					RankingManagerStats.RankingEXP[number] = rset.getInt("exp");
				}
				else
				{
					RankingManagerStats.RankingEXPName[number] = null;
					RankingManagerStats.RankingEXPClan[number] = null;
					RankingManagerStats.RankingEXPClass[number] = 0;
					RankingManagerStats.RankingEXPOn[number] = 0;
					RankingManagerStats.RankingEXP[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return;
	}
	
	private void selectRankingPK()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, pkkills FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY pkkills DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingPkName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingPkClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingPkClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingPkOn[number] = rset.getInt("online");
					RankingManagerStats.RankingPk[number] = rset.getInt("pkkills");
				}
				else
				{
					RankingManagerStats.RankingPkName[number] = null;
					RankingManagerStats.RankingPkClan[number] = null;
					RankingManagerStats.RankingPkClass[number] = 0;
					RankingManagerStats.RankingPkOn[number] = 0;
					RankingManagerStats.RankingPk[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void selectRankingRK()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, raidkills FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY raidkills DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingRaidName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingRaidClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingRaidClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingRaidOn[number] = rset.getInt("online");
					RankingManagerStats.RankingRaid[number] = rset.getInt("raidkills");
				}
				else
				{
					RankingManagerStats.RankingRaidName[number] = null;
					RankingManagerStats.RankingRaidClan[number] = null;
					RankingManagerStats.RankingRaidClass[number] = 0;
					RankingManagerStats.RankingRaidOn[number] = 0;
					RankingManagerStats.RankingRaid[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	// private void selectRankingCIS()
	// {
	// int number = 0;
	//
	// try (Connection con = DatabaseFactory.getConnection();
	// Statement st = con.createStatement();
	// ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, soloinstance FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY soloinstance DESC LIMIT " + 10))
	// {
	// while (rset.next())
	// {
	// if (!rset.getString("char_name").isEmpty())
	// {
	// RankingManagerStats.RankingInstanceSoloName[number] = rset.getString("char_name");
	// int clan_id = rset.getInt("clanid");
	// Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
	// RankingManagerStats.RankingInstanceSoloClan[number] = clan == null ? null : clan.getName();
	// RankingManagerStats.RankingInstanceSoloClass[number] = rset.getInt("class_id");
	// RankingManagerStats.RankingInstanceSoloOn[number] = rset.getInt("online");
	// RankingManagerStats.RankingInstanceSolo[number] = rset.getInt("soloinstance");
	// }
	// else
	// {
	// RankingManagerStats.RankingInstanceSoloName[number] = null;
	// RankingManagerStats.RankingInstanceSoloClan[number] = null;
	// RankingManagerStats.RankingInstanceSoloClass[number] = 0;
	// RankingManagerStats.RankingInstanceSoloOn[number] = 0;
	// RankingManagerStats.RankingInstanceSolo[number] = 0;
	// }
	// number++;
	// }
	// }
	// catch (Exception e)
	// {
	// e.printStackTrace();
	// }
	// }
	//
	// private void selectRankingCIP()
	// {
	// int number = 0;
	//
	// try (Connection con = DatabaseFactory.getConnection();
	// Statement st = con.createStatement();
	// ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, partyinstance FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) WHERE cs.isBase=1 AND accesslevel = 0 ORDER BY partyinstance DESC LIMIT " + 10))
	// {
	// while (rset.next())
	// {
	// if (!rset.getString("char_name").isEmpty())
	// {
	// RankingManagerStats.RankingInstancePartyName[number] = rset.getString("char_name");
	// int clan_id = rset.getInt("clanid");
	// Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
	// RankingManagerStats.RankingInstancePartyClan[number] = clan == null ? null : clan.getName();
	// RankingManagerStats.RankingInstancePartyClass[number] = rset.getInt("class_id");
	// RankingManagerStats.RankingInstancePartyOn[number] = rset.getInt("online");
	// RankingManagerStats.RankingInstanceParty[number] = rset.getInt("partyinstance");
	// }
	// else
	// {
	// RankingManagerStats.RankingInstancePartyName[number] = null;
	// RankingManagerStats.RankingInstancePartyClan[number] = null;
	// RankingManagerStats.RankingInstancePartyClass[number] = 0;
	// RankingManagerStats.RankingInstancePartyOn[number] = 0;
	// RankingManagerStats.RankingInstanceParty[number] = 0;
	// }
	// number++;
	// }
	// }
	// catch (Exception e)
	// {
	// e.printStackTrace();
	// }
	// }
	
	private void selectRankingAdena()
	{
		int number = 0;
		try (Connection con = DatabaseFactory.getConnection(); Statement st = con.createStatement(); ResultSet rset = st.executeQuery("SELECT char_name, class_id, clanid, online, it.count FROM characters AS c LEFT JOIN character_subclasses AS cs ON (c.obj_Id=cs.char_obj_id) JOIN items AS it ON (c.obj_Id=it.owner_id) WHERE cs.isBase=1 AND it.item_id=57 AND accesslevel = 0 ORDER BY it.count DESC LIMIT " + 10))
		{
			while (rset.next())
			{
				if (!rset.getString("char_name").isEmpty())
				{
					RankingManagerStats.RankingAdenaName[number] = rset.getString("char_name");
					int clan_id = rset.getInt("clanid");
					Clan clan = clan_id == 0 ? null : ClanTable.getInstance().getClan(clan_id);
					RankingManagerStats.RankingAdenaClan[number] = clan == null ? null : clan.getName();
					RankingManagerStats.RankingAdenaClass[number] = rset.getInt("class_id");
					RankingManagerStats.RankingAdenaOn[number] = rset.getInt("online");
					RankingManagerStats.RankingAdena[number] = rset.getLong("count");
				}
				else
				{
					RankingManagerStats.RankingAdenaName[number] = null;
					RankingManagerStats.RankingAdenaClan[number] = null;
					RankingManagerStats.RankingAdenaClass[number] = 0;
					RankingManagerStats.RankingAdenaOn[number] = 0;
					RankingManagerStats.RankingAdena[number] = 0;
				}
				number++;
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return;
	}
	
	@Override
	public String[] getCommunityBoardCommands()
	{
		return COMMANDS;
	}
	
	public static RankingCommunity getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final RankingCommunity INSTANCE = new RankingCommunity();
	}
}
