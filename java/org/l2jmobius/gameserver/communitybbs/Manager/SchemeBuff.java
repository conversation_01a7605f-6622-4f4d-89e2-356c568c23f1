/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.communitybbs.Manager;

/**
 * <AUTHOR>
 */
public class SchemeBuff
{
	public final int	skillId;
	public final int	skillLevel;
	public final int	forClass;
	public int			price;
	
	public SchemeBuff(int skillId, int skillLevel, int forClass, int price)
	{
		this.skillId = skillId;
		this.skillLevel = skillLevel;
		this.forClass = forClass;
		this.price = price;
	}
	
	// Getter for price
	public int getPrice()
	{
		return price;
	}
	
	// Setter for price
	public void setPrice(int price)
	{
		this.price = price;
	}
}
