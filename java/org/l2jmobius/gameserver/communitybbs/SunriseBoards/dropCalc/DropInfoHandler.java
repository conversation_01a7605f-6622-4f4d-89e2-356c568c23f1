package org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

import org.l2jmobius.gameserver.data.xml.NpcData;
import org.l2jmobius.gameserver.enums.DropType;
import org.l2jmobius.gameserver.model.holders.DropGroupHolder;
import org.l2jmobius.gameserver.model.holders.DropHolder;

public class DropInfoHandler
{
	protected static final Logger							_log				= Logger.getLogger(DropInfoHandler.class.getName());
	private final Map<Integer, ArrayList<DropInfoHolder>>	allItemDropIndex	= new HashMap<>();
	public static final Set<Integer>						HERBS				= new HashSet<>();
	
	public DropInfoHandler()
	{}
	
	public void load()
	{
		loadHerbList();
		buildDropIndex();
		_log.info(getClass().getSimpleName() + ": Loaded " + allItemDropIndex.size() + " drop data for calculator.");
	}
	
	private static void loadHerbList()
	{
		HERBS.addAll(new Range(8154, 8158).values()); // HERBs
		HERBS.addAll(new Range(8600, 8615).values()); // HERBs
		HERBS.addAll(new Range(8952, 8954).values()); // HERBs
		HERBS.addAll(new Range(10655, 10658).values()); // HERBs
		HERBS.addAll(new Range(10655, 10658).values()); // HERBs
		HERBS.add(13028);// Vitality Replenishing Herb
		HERBS.addAll(new Range(10432, 10434).values());// Kertin's Herb
	}
	
	public void addDropInfo(int npcId, DropInfoHolder drop)
	{
		int itemId = drop.getItemId(); // Ensure this method exists in DropInfoHolder
		ArrayList<DropInfoHolder> list;
		if ((list = getDrop(itemId)) == null)
		{
			list = new ArrayList<>();
			allItemDropIndex.put(itemId, list);
		}
		if (!list.contains(drop))
		{
			list.add(drop);
		}
	}
	
	private void buildDropIndex()
	{
		NpcData.getInstance().getTemplates(npc -> npc.getDropGroups() != null).forEach(npcTemplate ->
		{
			for (DropGroupHolder dropGroup : npcTemplate.getDropGroups())
			{
				final double chance = dropGroup.getChance() / 100;
				for (DropHolder dropHolder : dropGroup.getDropList())
				{
					if (!HERBS.contains(dropHolder.getItemId()))
					{
						addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), Math.min(100, dropHolder.getChance() * chance), dropHolder.getDropType() == DropType.SPOIL, dropHolder.getItemId()));
					}
				}
			}
		});
		NpcData.getInstance().getTemplates(npc -> npc.getDropList() != null).forEach(npcTemplate ->
		{
			for (DropHolder dropHolder : npcTemplate.getDropList())
			{
				if (!HERBS.contains(dropHolder.getItemId()))
				{
					addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), dropHolder.getDropType() == DropType.SPOIL, dropHolder.getItemId()));
				}
			}
		});
		NpcData.getInstance().getTemplates(npc -> npc.getSpoilList() != null).forEach(npcTemplate ->
		{
			for (DropHolder dropHolder : npcTemplate.getSpoilList())
			{
				if (!HERBS.contains(dropHolder.getItemId()))
				{
					addDropInfo(npcTemplate.getId(), new DropInfoHolder(npcTemplate.getId(), npcTemplate.getName(), npcTemplate.getLevel(), dropHolder.getMin(), dropHolder.getMax(), dropHolder.getChance(), dropHolder.getDropType() == DropType.SPOIL, dropHolder.getItemId()));
				}
			}
		});
		allItemDropIndex.values().forEach(list -> list.sort((d1, d2) -> Byte.valueOf(d1.getLevel()).compareTo(Byte.valueOf(d2.getLevel()))));
	}
	
	public ArrayList<DropInfoHolder> getDrop(int itemId)
	{
		return allItemDropIndex.get(itemId);
	}
	
	public Map<Integer, ArrayList<DropInfoHolder>> getInfo()
	{
		return allItemDropIndex;
	}
	
	public Comparator<DropInfoHolder> compareByChances = (o1, o2) ->
	{
		double level1 = o1.getChance();
		double level2 = o2.getChance();
		return level1 < level2 ? 1 : level1 == level2 ? 0 : -1;
	};
	
	public static DropInfoHandler getInstance()
	{
		return SingletonHolder._instance;
	}
	
	private static class SingletonHolder
	{
		protected static final DropInfoHandler _instance = new DropInfoHandler();
	}
}
