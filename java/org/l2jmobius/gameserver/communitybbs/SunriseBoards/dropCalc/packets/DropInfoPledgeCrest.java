package org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.packets;

import org.l2jmobius.Config;
import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

public class DropInfoPledgeCrest extends ServerPacket
{
	private final int		_crestId;
	private final byte[]	_data;
	
	public DropInfoPledgeCrest(int crestId, byte[] data)
	{
		_crestId = crestId;
		_data = data;
	}
	
	@Override
	protected void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.PLEDGE_CREST.writeId(this, buffer);
		buffer.writeInt(Config.SERVER_ID);
		buffer.writeInt(_crestId);
		if (_data != null)
		{
			buffer.writeInt(_data.length);
			buffer.writeBytes(_data);
		}
		else
		{
			buffer.writeInt(0);
		}
	}
}
