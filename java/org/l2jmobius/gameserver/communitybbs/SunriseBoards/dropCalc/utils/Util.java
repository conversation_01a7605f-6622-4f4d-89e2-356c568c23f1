package org.l2jmobius.gameserver.communitybbs.SunriseBoards.dropCalc.utils;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.util.Locale;

public class Util
{
	private static final char[]			ALLOWED_CHARS	=
	{
		'1',
		'2',
		'3',
		'4',
		'5',
		'6',
		'7',
		'8',
		'9',
		'0'
	};
	private static final NumberFormat	ADENA_FORMATTER	= NumberFormat.getIntegerInstance(Locale.ENGLISH);
	
	public static String formatAdena(long amount)
	{
		synchronized (ADENA_FORMATTER)
		{
			return ADENA_FORMATTER.format(amount);
		}
	}
	
	/**
	 * @param val
	 * @param format
	 * @return formatted double value by specified format.
	 */
	public static String formatDouble(double val, String format)
	{
		final DecimalFormat formatter = new DecimalFormat(format, new DecimalFormatSymbols(Locale.ENGLISH));
		return formatter.format(val);
	}
	
	public static boolean isInteger(char c)
	{
		for (char possibility : ALLOWED_CHARS)
		{
			if (possibility == c)
			{
				return true;
			}
		}
		return false;
	}
}
