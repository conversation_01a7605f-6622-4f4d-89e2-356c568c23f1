package org.l2jmobius.gameserver.features.balanceEngine.classBalancer;

import org.l2jmobius.gameserver.handler.IAdminCommandHandler;
import org.l2jmobius.gameserver.model.actor.Player;

public class AdminClassBalancer implements IAdminCommandHandler
{
	private static final String ADMIN_COMMANDS[] =
	{
		"admin_classbalancer",
		"admin_loadclassbalancer",
		"admin_updateclassbalancer"
	};
	
	@Override
	public boolean useAdminCommand(String command, Player activeChar)
	{
		if (command.startsWith("admin_classbalancer"))
		{
			ClassBalanceBBSManager.getInstance().parsecmd(command, activeChar);
		}
		else if (command.equalsIgnoreCase("admin_loadclassbalancer"))
		{
			ClassBalanceManager.getInstance().loadBalances();
			activeChar.sendMessage("Class balances has successfully been loaded!");
		}
		else if (command.equalsIgnoreCase("admin_updateclassbalancer"))
		{
			ClassBalanceManager.getInstance().updateBalances();
			activeChar.sendMessage("Class balances has successfully been updated!");
		}
		return true;
	}
	
	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
}
