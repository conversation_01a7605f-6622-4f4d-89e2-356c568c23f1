/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.geoengine.pathfinding;

import org.l2jmobius.gameserver.geoengine.geodata.GeoLocation;

/**
 * <AUTHOR>
 */
public class Node
{
	// node coords and nswe flag
	private GeoLocation	_loc;
	// node parent (for reverse path construction)
	private Node		_parent;
	// node child (for moving over nodes during iteration)
	private Node		_child;
	// node G cost (movement cost = parent movement cost + current movement cost)
	private double		_cost	= -1000;
	
	public void setLoc(int x, int y, int z)
	{
		// Reuse existing GeoLocation if possible to reduce memory allocation
		if (_loc == null)
		{
			_loc = new GeoLocation(x, y, z);
		}
		else
		{
			_loc.set(x, y, z);
		}
	}
	
	public GeoLocation getLoc()
	{
		return _loc;
	}
	
	public Node getParent()
	{
		return _parent;
	}
	
	public void setParent(Node parent)
	{
		_parent = parent;
	}
	
	public Node getChild()
	{
		return _child;
	}
	
	public void setChild(Node child)
	{
		_child = child;
	}
	
	public double getCost()
	{
		return _cost;
	}
	
	public void setCost(double cost)
	{
		_cost = cost;
	}
	
	public void free()
	{
		// reset node location
		_loc = null;
		// reset node parent, child and cost
		_parent = null;
		_child = null;
		_cost = -1000;
	}
}