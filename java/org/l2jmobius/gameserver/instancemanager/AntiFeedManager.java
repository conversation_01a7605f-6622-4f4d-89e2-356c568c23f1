/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.instancemanager;

import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.logging.Logger;

import org.l2jmobius.Config;
import org.l2jmobius.gameserver.model.actor.Creature;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.holders.ClientHardwareInfoHolder;
import org.l2jmobius.gameserver.model.olympiad.OlympiadManager;
import org.l2jmobius.gameserver.network.GameClient;

public class AntiFeedManager
{
	protected static final Logger							LOGGER_ACCOUNTING	= Logger.getLogger("accounting");
	public static final int									GAME_ID				= 0;
	public static final int									OLYMPIAD_ID			= 1;
	public static final int									TVT_ID				= 2;
	public static final int									L2EVENT_ID			= 3;
	public static final int									OFFLINE_PLAY		= 4;
	private final Map<Integer, Long>						_lastDeathTimes		= new ConcurrentHashMap<>();
	private final Map<Integer, Map<Integer, AtomicInteger>>	_eventIPs			= new ConcurrentHashMap<>();
	private final Map<String, Map<String, AtomicInteger>>	_eventHWIDs			= new ConcurrentHashMap<>();
	
	protected AntiFeedManager()
	{}
	
	/**
	 * Set time of the last player's death to current
	 * 
	 * @param objectId
	 *            Player's objectId
	 */
	public void setLastDeathTime(int objectId)
	{
		_lastDeathTimes.put(objectId, System.currentTimeMillis());
	}
	
	/**
	 * Check if current kill should be counted as non-feeded.
	 * 
	 * @param attacker
	 *            Attacker character
	 * @param target
	 *            Target character
	 * @return True if kill is non-feeded.
	 */
	public boolean check(Creature attacker, Creature target)
	{
		if (!Config.ANTIFEED_ENABLE)
		{
			return true;
		}
		if (target == null)
		{
			return false;
		}
		final Player targetPlayer = target.getActingPlayer();
		if (targetPlayer == null)
		{
			return false;
		}
		// Players in offline mode should't be valid targets.
		if (targetPlayer.getClient().isDetached())
		{
			return false;
		}
		if ((Config.ANTIFEED_INTERVAL > 0) && _lastDeathTimes.containsKey(targetPlayer.getObjectId()) && ((System.currentTimeMillis() - _lastDeathTimes.get(targetPlayer.getObjectId())) < Config.ANTIFEED_INTERVAL))
		{
			return false;
		}
		if (Config.ANTIFEED_DUALBOX && (attacker != null))
		{
			final Player attackerPlayer = attacker.getActingPlayer();
			if (attackerPlayer == null)
			{
				return false;
			}
			final GameClient targetClient = targetPlayer.getClient();
			final GameClient attackerClient = attackerPlayer.getClient();
			if ((targetClient == null) || (attackerClient == null) || targetClient.isDetached() || attackerClient.isDetached())
			{
				// unable to check ip address
				return !Config.ANTIFEED_DISCONNECTED_AS_DUALBOX;
			}
			return !targetClient.getIp().equals(attackerClient.getIp());
		}
		return true;
	}
	
	/**
	 * Clears all timestamps
	 */
	public void clear()
	{
		_lastDeathTimes.clear();
	}
	
	/**
	 * Register new event for dualbox check. Should be called only once.
	 * 
	 * @param eventId
	 */
	public void registerEvent(int eventId)
	{
		_eventIPs.putIfAbsent(eventId, new ConcurrentHashMap<>());
		_eventHWIDs.putIfAbsent(String.valueOf(eventId), new ConcurrentHashMap<>());
	}
	
	/**
	 * @param eventId
	 * @param player
	 * @param max
	 * @return If number of all simultaneous connections from player's IP address lower than max then increment connection count and return true.<br>
	 *         False if number of all simultaneous connections from player's IP address higher than max.
	 */
	public boolean tryAddPlayer(int eventId, Player player, int max)
	{
		return tryAddClient(eventId, player.getClient(), max);
	}
	
	/**
	 * @param eventId
	 * @param client
	 * @param max
	 * @return If number of all simultaneous connections from player's IP address lower than max then increment connection count and return true.<br>
	 *         False if number of all simultaneous connections from player's IP address higher than max.
	 */
	public boolean tryAddClient(int eventId, GameClient client, int max)
	{
		if (client == null)
		{
			return false; // unable to determine IP address
		}
		final Map<Integer, AtomicInteger> event = _eventIPs.get(eventId);
		if (event == null)
		{
			return false; // no such event registered
		}
		final Integer addrHash = client.getIp().hashCode();
		final AtomicInteger connectionCount = event.computeIfAbsent(addrHash, k -> new AtomicInteger());
		if ((connectionCount.get() + 1) <= (max + Config.DUALBOX_CHECK_WHITELIST.getOrDefault(addrHash, 0)))
		{
			connectionCount.incrementAndGet();
			return true;
		}
		return false;
	}
	
	/**
	 * Decreasing number of active connection from player's IP address
	 * 
	 * @param eventId
	 * @param player
	 * @return true if success and false if any problem detected.
	 */
	public boolean removePlayer(int eventId, Player player)
	{
		return removeClient(eventId, player.getClient());
	}
	
	/**
	 * Decreasing number of active connection from player's IP address
	 * 
	 * @param eventId
	 * @param client
	 * @return true if success and false if any problem detected.
	 */
	public boolean removeClient(int eventId, GameClient client)
	{
		if (client == null)
		{
			return false; // unable to determine IP address
		}
		final Map<Integer, AtomicInteger> event = _eventIPs.get(eventId);
		if (event == null)
		{
			return false; // no such event registered
		}
		final Integer addrHash = client.getIp().hashCode();
		return event.computeIfPresent(addrHash, (k, v) ->
		{
			if ((v == null) || (v.decrementAndGet() == 0))
			{
				return null;
			}
			return v;
		}) != null;
	}
	
	/**
	 * Remove player connection IP address from all registered events lists.
	 * 
	 * @param client
	 */
	public void onDisconnect(GameClient client)
	{
		if (client == null)
		{
			return;
		}
		final Player player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (player.isInOfflineMode())
		{
			return;
		}
		final String clientIp = client.getIp();
		if (clientIp == null)
		{
			return;
		}
		for (Entry<Integer, Map<Integer, AtomicInteger>> entry : _eventIPs.entrySet())
		{
			final int eventId = entry.getKey();
			if (eventId == OLYMPIAD_ID)
			{
				final AtomicInteger count = entry.getValue().get(clientIp.hashCode());
				if ((count != null) && (OlympiadManager.getInstance().isRegistered(player) || (player.getOlympiadGameId() != -1)))
				{
					count.decrementAndGet();
				}
			}
			else
			{
				removeClient(eventId, client);
			}
		}
	}
	
	/**
	 * Clear all entries for this eventId.
	 * 
	 * @param eventId
	 */
	public void clear(int eventId)
	{
		final Map<Integer, AtomicInteger> event = _eventIPs.get(eventId);
		if (event != null)
		{
			event.clear();
		}
		final Map<String, AtomicInteger> eventHWID = _eventHWIDs.get(String.valueOf(eventId));
		if (eventHWID != null)
		{
			eventHWID.clear();
		}
	}
	
	/**
	 * @param player
	 * @param max
	 * @return maximum number of allowed connections (whitelist + max)
	 */
	public int getLimit(Player player, int max)
	{
		return getLimit(player.getClient(), max);
	}
	
	/**
	 * @param client
	 * @param max
	 * @return maximum number of allowed connections (whitelist + max)
	 */
	public int getLimit(GameClient client, int max)
	{
		if (client == null)
		{
			return max;
		}
		final Integer addrHash = client.getIp().hashCode();
		int limit = max;
		if (Config.DUALBOX_CHECK_WHITELIST.containsKey(addrHash))
		{
			limit += Config.DUALBOX_CHECK_WHITELIST.get(addrHash);
		}
		return limit;
	}
	
	public int getLimitByHwid(ClientHardwareInfoHolder hwid, int max)
	{
		if (hwid == null)
		{
			return max;
		}
		String macAddress = hwid.getMacAddress();
		if (Config.DUALBOX_CHECK_WHITELIST_HWID.containsKey(macAddress))
		{
			int whitelistLimit = Config.DUALBOX_CHECK_WHITELIST_HWID.get(macAddress);
			return whitelistLimit == -1 ? Integer.MAX_VALUE : whitelistLimit; // Không giới hạn nếu -1
		}
		return max;
	}
	
	public boolean tryAddPlayerByHwid(int eventId, ClientHardwareInfoHolder hwid, int max)
	{
		if (hwid == null)
		{
			return false; // Không thể xác định HWID
		}
		final Map<String, AtomicInteger> event = _eventHWIDs.get(String.valueOf(eventId));
		if (event == null)
		{
			return false; // Không có sự kiện nào được đăng ký
		}
		final AtomicInteger connectionCount = event.computeIfAbsent(hwid.getMacAddress(), k -> new AtomicInteger());
		if ((connectionCount.get() + 1) <= max)
		{
			connectionCount.incrementAndGet();
			return true;
		}
		return false;
	}
	
	public boolean removePlayerByHwid(int eventId, String hwid)
	{
		if (hwid == null || hwid.isEmpty())
		{
			return false; // Unable to determine HWID
		}
		final Map<String, AtomicInteger> event = _eventHWIDs.get(String.valueOf(eventId));
		if (event == null)
		{
			return false; // No such event registered
		}
		return event.computeIfPresent(hwid, (k, v) ->
		{
			if ((v == null) || (v.decrementAndGet() == 0))
			{
				return null;
			}
			return v;
		}) != null;
	}
	
	public void onDisconnectByHwid(String hwid)
	{
		if (hwid == null)
		{
			return;
		}
		for (Entry<String, Map<String, AtomicInteger>> entry : _eventHWIDs.entrySet())
		{
			removePlayerByHwid(Integer.parseInt(entry.getKey()), hwid);
		}
	}
	
	public static AntiFeedManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final AntiFeedManager INSTANCE = new AntiFeedManager();
	}
}