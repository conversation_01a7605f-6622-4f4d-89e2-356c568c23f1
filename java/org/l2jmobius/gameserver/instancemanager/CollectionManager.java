package org.l2jmobius.gameserver.instancemanager;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import org.l2jmobius.Config;
import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.data.xml.CollectionData;
import org.l2jmobius.gameserver.data.xml.ItemData;
import org.l2jmobius.gameserver.data.xml.OptionData;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.holders.CollectionDataHolder;
import org.l2jmobius.gameserver.model.holders.ItemCollectionData;
import org.l2jmobius.gameserver.model.options.Options;

public class CollectionManager
{
	private static final Logger								LOGGER							= Logger.getLogger(CollectionManager.class.getName());
	private final Map<Integer, CollectionDataHolder>		_collections					= new ConcurrentHashMap<>();
	private final Map<Player, Map<Integer, List<Integer>>>	playerCollectionProgress		= new ConcurrentHashMap<>();
	private static int										LCOIN							= 91663;
	private Map<Integer, String>							optionDescriptions				= new ConcurrentHashMap<>();
	private final Map<Player, Map<Integer, List<Integer>>>	playerCollectionProgressBySlot	= new ConcurrentHashMap<>();
	
	public CollectionDataHolder getCollectionById(int collectionId)
	{
		return _collections.get(collectionId);
	}
	
	public List<CollectionDataHolder> getCollectionsByCategory(int category)
	{
		return _collections.values().stream().filter(collection -> collection.getCategory() == category).collect(Collectors.toList());
	}
	
	public void showCollectionHome(Player player)
	{
		if (!Config.ENABLE_COLLECTION_SYSTEM)
		{
			player.sendMessage("The collection system is currently disabled.");
			return;
		}
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/collection/home.html");
		if (html != null)
		{
			if (player.isGM())
			{
				String adminButtonHtml = "<table width=470>" + "<tr>" + "<td align=center>" + "<button action=\"bypass _bbadminPlayerManagement\" value=\"Admin Collection Management\" width=200 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button>" + "</td>" + "</tr>" + "</table>";
				html = html.replace("%adminButton%", adminButtonHtml);
			}
			else
			{
				html = html.replace("%adminButton%", "");
			}
			CommunityBoardHandler.separateAndSend(html, player);
		}
		else
		{
			player.sendMessage("Collection home page not found.");
		}
	}
	
	public void showCategory(Player player, int category)
	{
		showCategory(player, category, 0); // Default to first page
	}
	
	public void showCategory(Player player, int category, int page)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/collection/category.html");
		if (html == null)
		{
			player.sendMessage("Category page not found.");
			return;
		}
		String categoryName = getCategoryName(category);
		List<CollectionDataHolder> collections = CollectionData.getInstance().getCollectionsByCategory(category);
		int itemsPerPage = 6;
		int maxPage = (int) Math.ceil((double) collections.size() / itemsPerPage);
		if (page > maxPage)
		{
			page = 1;
		}
		StringBuilder collectionsHtml = new StringBuilder();
		int start = (page - 1) * itemsPerPage;
		int end = Math.min(start + itemsPerPage, collections.size());
		for (int i = start; i < end; i++)
		{
			CollectionDataHolder collection = collections.get(i);
			boolean isCollectionComplete = checkIfCollectionComplete(player, collection);
			collectionsHtml.append("<tr>");
			if (isCollectionComplete)
			{
				collectionsHtml.append("<td align=\"center\"><font color=\"00FF00\">").append(collection.getName()).append("</font></td>");
				collectionsHtml.append("<td align=\"center\"><font color=\"00FF00\">").append(getEffectDescription(collection.getOptionId())).append("</font></td>");
			}
			else
			{
				collectionsHtml.append("<td align=\"center\"><font color=\"FFD700\">").append(collection.getName()).append("</font></td>");
				collectionsHtml.append("<td align=\"center\"><font color=\"FFD700\">").append(getEffectDescription(collection.getOptionId())).append("</font></td>");
			}
			collectionsHtml.append("<td align=\"center\"><button action=\"bypass _bbshowCollectionDetails ").append(collection.getCollectionId()).append(" ").append(category).append(" ").append(page).append("\" value=\"View\" width=75 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>");
			if (collection.getBuyCollection() > 0)
			{
				collectionsHtml.append("<td align=\"center\"><button action=\"bypass _bbsbuyCollection ").append(collection.getCollectionId()).append(" ").append(category).append(" ").append(page).append("\" value=\"").append(collection.getBuyCollection()).append(" L-Coins\" width=75 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button></td>");
			}
			collectionsHtml.append("</tr>");
		}
		String previousPageButton = "";
		if (page > 1)
		{
			previousPageButton = "<button action=\"bypass _bbshowCategory " + category + " " + (page - 1) + "\" value=\"Previous Page\" width=100 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button>";
		}
		String backToPage1Button = "";
		if (page > 1)
		{
			backToPage1Button = "<button action=\"bypass _bbshowCategory " + category + " 1\" value=\"Back to Page 1\" width=100 height=30 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button>";
		}
		html = html.replace("%categoryName%", categoryName);
		html = html.replace("%collections%", collectionsHtml.toString());
		html = html.replace("%category%", String.valueOf(category));
		html = html.replace("%nextPage%", String.valueOf(page + 1));
		html = html.replace("%previousPageButton%", previousPageButton);
		html = html.replace("%backToPage1Button%", backToPage1Button);
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	private String getCategoryName(int category)
	{
		switch (category)
		{
			case 1:
				return "Attack";
			case 2:
				return "Defense";
			case 3:
				return "Support";
			case 4:
				return "Special";
			case 5:
				return "Stats";
			case 6:
				return "Convenience";
			default:
				return "Unknown";
		}
	}
	
	private String getEffectDescription(int optionId)
	{
		String description = optionDescriptions.get(optionId);
		return description != null ? description : "No effect";
	}
	
	public synchronized void showCollectionDetails(Player player, int collectionId, int currentCategory, int currentPage)
	{
		CollectionDataHolder collection = CollectionData.getInstance().getCollection(collectionId);
		if (collection == null)
		{
			player.sendMessage("Collection not found.");
			return;
		}
		// Tải lại giao diện và cập nhật thông tin thu thập
		StringBuilder mainItemsHtml = new StringBuilder();
		Map<Integer, List<Integer>> collectedSlots = playerCollectionProgressBySlot.getOrDefault(player, new ConcurrentHashMap<>());
		List<Integer> collectedItems = collectedSlots.getOrDefault(collectionId, new ArrayList<>());
		for (Map.Entry<Integer, List<ItemCollectionData>> entry : collection.getSlotMap().entrySet())
		{
			int slot = entry.getKey();
			boolean slotCollected = collectedItems.contains(slot);
			for (ItemCollectionData itemData : entry.getValue())
			{
				addHtmlForItem(player, collectionId, slot, mainItemsHtml, itemData, slotCollected, currentCategory, currentPage);
			}
		}
		// Cập nhật HTML và gửi đến người chơi
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/collection/collection_details.html");
		html = html.replace("%collectionName%", collection.getName());
		html = html.replace("%mainItems%", mainItemsHtml.toString());
		html = html.replace("%currentCategory%", String.valueOf(currentCategory));
		html = html.replace("%currentPage%", String.valueOf(currentPage));
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	private void addHtmlForItem(Player player, int collectionId, int slot, StringBuilder htmlBuilder, ItemCollectionData itemData, boolean slotCollected, int currentCategory, int currentPage)
	{
		List<Integer> itemIds = itemData.getItemIds();
		long requiredItemCount = itemData.getCount();
		int enchantLevel = itemData.getEnchantLevel();
		// Duyệt qua tất cả itemIds và hiển thị
		for (int itemId : itemIds)
		{
			var itemTemplate = ItemData.getInstance().getTemplate(itemId);
			if (itemTemplate != null)
			{
				String itemName = itemTemplate.getName();
				String displayName = enchantLevel > 0 ? itemName + " +" + enchantLevel : itemName;
				htmlBuilder.append("<tr>");
				htmlBuilder.append("<td height=40 align=\"center\"><img src=\"").append(itemTemplate.getIcon()).append("\" width=32 height=32></td>");
				if (slotCollected)
				{
					htmlBuilder.append("<td align=\"center\"><font color=\"00FF00\">").append(displayName).append("</font></td>");
					htmlBuilder.append("<td align=\"center\"><font color=\"00FF00\">").append(requiredItemCount).append("/").append(requiredItemCount).append("</font></td>");
					htmlBuilder.append("<td align=\"center\"><font color=\"00FF00\">Collected</font></td>");
				}
				else
				{
					htmlBuilder.append("<td align=\"center\"><font color=\"FFD700\">").append(displayName).append("</font></td>");
					htmlBuilder.append("<td align=\"center\"><font color=\"FFD700\">0/").append(requiredItemCount).append("</font></td>");
					// Thay thế %currentCategory% và %currentPage% bằng giá trị thực
					htmlBuilder.append("<td align=\"center\"><button action=\"bypass _bbcollectItem ").append(collectionId).append(" ").append(itemId).append(" ").append(slot).append(" ").append(currentCategory).append(" ").append(currentPage).append("\" value=\"Collect\" width=75 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button></td>");
				}
				htmlBuilder.append("</tr>");
			}
		}
	}
	
	public synchronized void collectItem(Player player, int collectionId, int itemId, int slot, int currentCategory, int currentPage)
	{
		if (!Config.ENABLE_COLLECTION_SYSTEM)
		{
			player.sendMessage("The collection system is currently disabled.");
			return;
		}
		CollectionDataHolder collection = CollectionData.getInstance().getCollection(collectionId);
		if (collection == null)
		{
			player.sendMessage("Collection not found.");
			return;
		}
		Map<Integer, List<ItemCollectionData>> slotMap = collection.getSlotMap();
		if (slotMap == null || !slotMap.containsKey(slot))
		{
			player.sendMessage("No items available for slot: " + slot);
			return;
		}
		List<ItemCollectionData> slotItems = slotMap.get(slot);
		boolean isCollected = false;
		for (ItemCollectionData item : slotItems)
		{
			if (item.getItemIds().contains(itemId))
			{
				if (collectSpecificItem(player, collectionId, itemId, item.getCount(), item.getEnchantLevel()))
				{
					markSlotAsCollected(player, collectionId, slot);
					saveCollectionProgress(player, collectionId, itemId, slot);
					isCollected = true;
					break;
				}
			}
		}
		if (isCollected)
		{
			if (checkIfCollectionComplete(player, collection))
			{
				applyCollectionOption(player, collection.getOptionId());
				saveCollectionStatus(player, collectionId, collection.getOptionId());
			}
			updateCollectionUI(player, collectionId, currentCategory, currentPage);
		}
		else
		{
			player.sendMessage("You do not have the required items to collect.");
		}
	}
	
	private void markSlotAsCollected(Player player, int collectionId, int slot)
	{
		playerCollectionProgressBySlot.putIfAbsent(player, new ConcurrentHashMap<>());
		playerCollectionProgressBySlot.get(player).putIfAbsent(collectionId, new ArrayList<>());
		// Kiểm tra xem slot đã được đánh dấu chưa
		if (!playerCollectionProgressBySlot.get(player).get(collectionId).contains(slot))
		{
			playerCollectionProgressBySlot.get(player).get(collectionId).add(slot);
		}
	}
	
	private boolean collectSpecificItem(Player player, int collectionId, int itemId, long itemCount, int enchantLevel)
	{
		long playerItemCount = player.getInventory().getInventoryItemCount(itemId, -1);
		var inventoryItem = player.getInventory().getItemByItemId(itemId);
		if (playerItemCount >= itemCount && (inventoryItem == null || inventoryItem.getEnchantLevel() >= enchantLevel))
		{
			player.getInventory().destroyItemByItemId("Collection", itemId, itemCount, player, true);
			return true;
		}
		return false;
	}
	
	public void updateCollectionUI(Player player, int collectionId, int currentCategory, int currentPage)
	{
		showCollectionDetails(player, collectionId, currentCategory, currentPage);
	}
	
	private boolean checkIfCollectionComplete(Player player, CollectionDataHolder collection)
	{
		Map<Integer, List<Integer>> playerProgress = playerCollectionProgressBySlot.get(player);
		if (playerProgress == null || !playerProgress.containsKey(collection.getCollectionId()))
		{
			return false;
		}
		List<Integer> collectedSlots = playerProgress.get(collection.getCollectionId());
		for (int slot : collection.getSlotMap().keySet())
		{
			if (!collectedSlots.contains(slot))
			{
				return false;
			}
		}
		return true;
	}
	
	public synchronized void buyCollection(Player player, int collectionId, int currentCategory, int currentPage)
	{
		if (!Config.ENABLE_COLLECTION_SYSTEM)
		{
			player.sendMessage("The collection system is currently disabled.");
			return;
		}
		CollectionDataHolder collection = CollectionData.getInstance().getCollection(collectionId);
		if (collection == null)
		{
			player.sendMessage("Collection not found.");
			return;
		}
		if (checkIfCollectionComplete(player, collection))
		{
			player.sendMessage("You have already completed this collection.");
			return;
		}
		int buyPrice = collection.getBuyCollection();
		if (buyPrice <= 0)
		{
			player.sendMessage("This collection cannot be purchased.");
			return;
		}
		long playerDonateCoinCount = player.getInventory().getInventoryItemCount(LCOIN, -1);
		if (playerDonateCoinCount >= buyPrice)
		{
			player.getInventory().destroyItemByItemId("BuyCollection", LCOIN, buyPrice, player, true);
			for (Map.Entry<Integer, List<ItemCollectionData>> entry : collection.getSlotMap().entrySet())
			{
				int slot = entry.getKey();
				for (ItemCollectionData item : entry.getValue())
				{
					int firstItemId = item.getItemIds().get(0);
					markItemAsCollected(player, collectionId, firstItemId);
					markSlotAsCollected(player, collectionId, slot);
					saveCollectionProgress(player, collectionId, firstItemId, slot);
				}
			}
			applyCollectionOption(player, collection.getOptionId());
			saveCollectionStatus(player, collectionId, collection.getOptionId());
			updateCollectionUI(player, collectionId, currentCategory, currentPage);
		}
		else
		{
			player.sendMessage("You do not have enough L-Coins to purchase this collection.");
		}
	}
	
	public void addCollection(Player player, int collectionId)
	{
		CollectionDataHolder collection = CollectionData.getInstance().getCollection(collectionId);
		if (collection == null)
		{
			player.sendMessage("Collection not found.");
			return;
		}
		if (checkIfCollectionComplete(player, collection))
		{
			player.sendMessage("You have already completed this collection.");
			return;
		}
		for (Map.Entry<Integer, List<ItemCollectionData>> entry : collection.getSlotMap().entrySet())
		{
			int slot = entry.getKey();
			for (ItemCollectionData item : entry.getValue())
			{
				int firstItemId = item.getItemIds().get(0);
				markItemAsCollected(player, collectionId, firstItemId);
				markSlotAsCollected(player, collectionId, slot);
				saveCollectionProgress(player, collectionId, firstItemId, slot);
			}
		}
		applyCollectionOption(player, collection.getOptionId());
		saveCollectionStatus(player, collectionId, collection.getOptionId());
		updatePlayerCollectionUI(player);
		updateAdminCollectionUI(player, player);
	}
	
	private void markItemAsCollected(Player player, int collectionId, int itemId)
	{
		playerCollectionProgress.putIfAbsent(player, new ConcurrentHashMap<>());
		playerCollectionProgress.get(player).putIfAbsent(collectionId, new ArrayList<>());
		if (!playerCollectionProgress.get(player).get(collectionId).contains(itemId))
		{
			playerCollectionProgress.get(player).get(collectionId).add(itemId);
		}
		CollectionDataHolder collection = getCollectionById(collectionId);
		if (collection != null)
		{
			for (Map.Entry<Integer, List<ItemCollectionData>> entry : collection.getSlotMap().entrySet())
			{
				for (ItemCollectionData holder : entry.getValue())
				{
					if (holder.getItemIds().contains(itemId))
					{
						markSlotAsCollected(player, collectionId, entry.getKey());
					}
				}
			}
		}
	}
	
	private void applyCollectionOption(Player player, int optionId)
	{
		final Options option = OptionData.getInstance().getOptions(optionId);
		if (option != null)
		{
			option.apply(player);
			player.sendMessage("You have received the special effect from the Collection!");
		}
		else
		{
			player.sendMessage("Corresponding Option not found.");
		}
	}
	
	private void removeCollectionOption(Player player, int collectionId)
	{
		CollectionDataHolder collection = getCollectionById(collectionId);
		if (collection != null)
		{
			final Options option = OptionData.getInstance().getOptions(collection.getOptionId());
			if (option != null)
			{
				option.remove(player);
				player.sendMessage("The special effect from the Collection has been removed.");
			}
		}
	}
	
	public void saveCollectionProgress(Player player, int collectionId, int itemId, int slot)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO collection_progress (player_id, collection_id, item_id, slot) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE item_id = VALUES(item_id), slot = VALUES(slot)"))
		{
			ps.setInt(1, player.getObjectId());
			ps.setInt(2, collectionId);
			ps.setInt(3, itemId);
			ps.setInt(4, slot);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, "Failed to save collection progress for player: " + player.getName(), e);
		}
	}
	
	public void saveCollectionStatus(Player player, int collectionId, int optionId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement psStatus = con.prepareStatement("INSERT INTO collection_status (player_id, collection_id, option_id) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE option_id = VALUES(option_id)"))
		{
			psStatus.setInt(1, player.getObjectId());
			psStatus.setInt(2, collectionId);
			psStatus.setInt(3, optionId);
			psStatus.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, "Failed to save collection status for player: " + player.getName(), e);
		}
	}
	
	private void deleteCollectionStatus(Player player, int collectionId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM collection_status WHERE player_id = ? AND collection_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			ps.setInt(2, collectionId);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, "Failed to delete collection status for player: " + player.getName(), e);
		}
	}
	
	public void loadPlayerCollectionProgress(Player player)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			try (PreparedStatement ps = con.prepareStatement("SELECT collection_id, item_id, slot FROM collection_progress WHERE player_id = ?"))
			{
				ps.setInt(1, player.getObjectId());
				try (ResultSet rs = ps.executeQuery())
				{
					while (rs.next())
					{
						int collectionId = rs.getInt("collection_id");
						int itemId = rs.getInt("item_id");
						int slot = rs.getInt("slot");
						// Save to playerCollectionProgress
						playerCollectionProgress.putIfAbsent(player, new ConcurrentHashMap<>());
						playerCollectionProgress.get(player).putIfAbsent(collectionId, new ArrayList<>());
						playerCollectionProgress.get(player).get(collectionId).add(itemId);
						// Save to playerCollectionProgressBySlot
						playerCollectionProgressBySlot.putIfAbsent(player, new ConcurrentHashMap<>());
						playerCollectionProgressBySlot.get(player).putIfAbsent(collectionId, new ArrayList<>());
						if (!playerCollectionProgressBySlot.get(player).get(collectionId).contains(slot))
						{
							playerCollectionProgressBySlot.get(player).get(collectionId).add(slot);
						}
					}
				}
			}
			// Load and apply option_id from collection_status for each collection
			try (PreparedStatement psStatus = con.prepareStatement("SELECT collection_id, option_id FROM collection_status WHERE player_id = ?"))
			{
				psStatus.setInt(1, player.getObjectId());
				try (ResultSet rsStatus = psStatus.executeQuery())
				{
					while (rsStatus.next())
					{
						applyCollectionOption(player, rsStatus.getInt("option_id"));
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, "Failed to load collection progress for player: " + player.getName(), e);
		}
	}
	
	public void deleteCollectionProgress(Player player, int collectionId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("DELETE FROM collection_progress WHERE player_id = ? AND collection_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			ps.setInt(2, collectionId);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			LOGGER.log(Level.WARNING, "Failed to delete collection progress for player: " + player.getName(), e);
		}
	}
	
	public void loadOptionDescriptions()
	{
		try (BufferedReader br = new BufferedReader(new FileReader("data/FormattedCollectionDescriptions.txt")))
		{
			String line;
			while ((line = br.readLine()) != null)
			{
				String[] parts = line.split(" ", 2);
				if (parts.length == 2)
				{
					int optionId = Integer.parseInt(parts[0].trim());
					String description = parts[1].trim();
					optionDescriptions.put(optionId, description);
				}
			}
		}
		catch (IOException e)
		{
			e.printStackTrace();
		}
	}
	
	public void showPlayerManagement(Player admin)
	{
		String html = HtmCache.getInstance().getHtm(admin, "data/html/CommunityBoard/Custom/collection/admin/admin_player_management.html");
		if (html != null)
		{
			CommunityBoardHandler.separateAndSend(html, admin);
		}
		else
		{
			admin.sendMessage("Player management page not found.");
		}
	}
	
	public void viewPlayerCollections(Player admin, String playerName)
	{
		Player targetPlayer = World.getInstance().getPlayer(playerName);
		if (targetPlayer == null)
		{
			admin.sendMessage("Player " + playerName + " not found.");
			return;
		}
		loadPlayerCollectionProgress(targetPlayer);
		String html = HtmCache.getInstance().getHtm(admin, "data/html/CommunityBoard/Custom/collection/admin/player_collections.html");
		if (html == null)
		{
			admin.sendMessage("Player collections page not found.");
			return;
		}
		StringBuilder collectionsHtml = new StringBuilder();
		for (CollectionDataHolder collection : CollectionData.getInstance().getCollections())
		{
			boolean isComplete = checkIfCollectionComplete(targetPlayer, collection);
			if (isComplete)
			{
				collectionsHtml.append("<tr>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\"><font color=\"LEVEL\">").append(collection.getName()).append("</font></td>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\"><font color=\"00FF00\">Completed</font></td>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\">");
				collectionsHtml.append("<button action=\"bypass _bbadminDeleteCollection ").append(targetPlayer.getObjectId()).append(" ").append(collection.getCollectionId()).append("\" value=\"Delete\" width=75 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button>");
				collectionsHtml.append("</td>");
				collectionsHtml.append("</tr>");
			}
		}
		html = html.replace("%playerName%", targetPlayer.getName());
		html = html.replace("%collections%", collectionsHtml.toString());
		CommunityBoardHandler.separateAndSend(html, admin);
	}
	
	public void deleteCollection(Player admin, int playerId, int collectionId)
	{
		Player player = World.getInstance().getPlayer(playerId);
		if (player == null)
		{
			admin.sendMessage("Player not found.");
			return;
		}
		if (playerCollectionProgress.containsKey(player))
		{
			playerCollectionProgress.get(player).remove(collectionId);
		}
		if (playerCollectionProgressBySlot.containsKey(player))
		{
			playerCollectionProgressBySlot.get(player).remove(collectionId);
		}
		deleteCollectionProgress(player, collectionId);
		deleteCollectionStatus(player, collectionId);
		removeCollectionOption(player, collectionId);
		updatePlayerCollectionUI(player);
		updateAdminCollectionUI(admin, player);
		admin.sendMessage("Collection " + collectionId + " has been deleted from player " + player.getName());
	}
	
	private void updatePlayerCollectionUI(Player player)
	{
		showCollectionHome(player);
	}
	
	private void updateAdminCollectionUI(Player admin, Player targetPlayer)
	{
		String html = HtmCache.getInstance().getHtm(admin, "data/html/CommunityBoard/Custom/collection/admin/player_collections.html");
		if (html == null)
		{
			admin.sendMessage("Player collections page not found.");
			return;
		}
		StringBuilder collectionsHtml = new StringBuilder();
		for (CollectionDataHolder collection : CollectionData.getInstance().getCollections())
		{
			boolean isComplete = checkIfCollectionComplete(targetPlayer, collection);
			if (isComplete)
			{
				collectionsHtml.append("<tr>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\"><font color=\"LEVEL\">").append(collection.getName()).append("</font></td>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\"><font color=\"00FF00\">Completed</font></td>");
				collectionsHtml.append("<td align=center bgcolor=\"2E2E2E\">");
				collectionsHtml.append("<button action=\"bypass _bbadminDeleteCollection ").append(targetPlayer.getObjectId()).append(" ").append(collection.getCollectionId()).append("\" value=\"Delete\" width=75 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></button>");
				collectionsHtml.append("</td>");
				collectionsHtml.append("</tr>");
			}
		}
		html = html.replace("%playerName%", targetPlayer.getName());
		html = html.replace("%collections%", collectionsHtml.toString());
		CommunityBoardHandler.separateAndSend(html, admin);
	}
	
	public static CollectionManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final CollectionManager INSTANCE = new CollectionManager();
	}
}
