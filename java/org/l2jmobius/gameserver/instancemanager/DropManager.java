/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ScheduledFuture;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.commons.util.Rnd;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.actor.instance.Monster;
import org.l2jmobius.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import org.l2jmobius.gameserver.model.events.impl.creature.player.OnPlayerLogout;

public class DropManager
{
	private List<Schedule>					HOT_TIME_SCHEDULE		= new ArrayList<>();
	private List<Drop>						DROP_LIST				= new ArrayList<>();
	private int								MAX_LEVEL_DIFFERENCE	= 9;
	private double							DROP_BOOST				= 1.0;
	private boolean							BUFF_ACTIVE				= false;
	private Schedule						BUFF_SCHEDULE;
	//
	private SimpleDateFormat				DATE_FORMATTER			= new SimpleDateFormat("yyyy-MM-dd");
	private Map<String, Map<Integer, Long>>	DATA_ACTIVE				= new HashMap<>();
	private Map<String, Map<Integer, Long>>	DATA_PASSIVE			= new HashMap<>();
	//
	private final static int				LCOIN					= 91663;
	
	protected DropManager()
	{
		// Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_LOGIN, (OnPlayerLogin event) -> onPlayerLogin(event), this));
		// Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_LOGOUT, (OnPlayerLogout event) -> onPlayerLogout(event), this));
		loadActiveSql();
		loadPassiveSql();
		HOT_TIME_SCHEDULE.add(new Schedule("16:05:00", "17:00:00", 2.0));
		HOT_TIME_SCHEDULE.add(new Schedule("19:42:00", "20:00:00", 2.0));
		// DROP_LIST.add(new Drop(57, 100_000, 200_000, 50, 1_000_000));
		DROP_LIST.add(new Drop(LCOIN, 1, 2, 0, 8000, false)); // Normal Char
		DROP_LIST.add(new Drop(LCOIN, 4, 6, 21, 13000, true)); // premium Char
		BUFF_SCHEDULE = new Schedule("00:00:00", "23:59:00", 0);
		final Calendar c = Calendar.getInstance();
		c.set(Calendar.MINUTE, 0);
		c.set(Calendar.SECOND, 0);
		c.set(Calendar.MILLISECOND, 0);
		while (c.getTimeInMillis() < System.currentTimeMillis())
		{
			c.add(Calendar.MINUTE, 1);
		}
		long init = c.getTimeInMillis() - System.currentTimeMillis();
		ThreadPool.scheduleAtFixedRate(() ->
		{
			checkSchedule();
		}, init, 1 * 60 * 1000);
		ThreadPool.scheduleAtFixedRate(() ->
		{
			saveActiveSql();
		}, 1 * 60 * 1000, 1 * 60 * 1000);
		ThreadPool.scheduleAtFixedRate(() ->
		{
			savePassiveSql();
		}, 1 * 60 * 1000, 1 * 60 * 1000);
		checkSchedule();
		initializeDailyResetSchedule(); // Gọi hàm lên lịch reset mới
	}
	
	private void initializeDailyResetSchedule()
	{
		// Thiết lập thời gian mục tiêu cho reset vào 6h30 sáng
		final Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, 6);
		calendar.set(Calendar.MINUTE, 30);
		calendar.set(Calendar.SECOND, 0);
		calendar.set(Calendar.MILLISECOND, 0);
		// Nếu thời gian mục tiêu đã trôi qua hôm nay, lên lịch cho ngày mai
		if (calendar.getTimeInMillis() < System.currentTimeMillis())
		{
			calendar.add(Calendar.DAY_OF_YEAR, 1); // Đặt lại lịch cho ngày mai
		}
		// Thời gian trì hoãn trước khi thực thi lần đầu tiên
		long startDelay = calendar.getTimeInMillis() - System.currentTimeMillis();
		// Đặt lịch để chạy lại sau mỗi 24 giờ
		long repeatInterval = 24 * 60 * 60 * 1000; // 24 giờ
		ThreadPool.scheduleAtFixedRate(() -> resetLimitLcoin(), startDelay, repeatInterval);
	}
	
	// Giả sử đây là phần xử lý khi người chơi đăng nhập
	public void onPlayerLogin(OnPlayerLogin event)
	{
		final Player player = event.getPlayer();
		startPassiveDrop(player);
	}
	
	public void onPlayerLogout(OnPlayerLogout event)
	{
		final Player player = event.getPlayer();
		stopPassiveDrop(player);
	}
	
	public void resetLimitLcoin()
	{
		DATA_ACTIVE.clear();
		DATA_PASSIVE.clear();
	}
	
	public int getMaxDrop(Player player, int itemId)
	{
		boolean p = PremiumManager.getInstance().getPremiumExpiration(player.getAccountName()) > 0;
		for (Drop drop : DROP_LIST)
		{
			if (drop.premium == p)
			{
				if (drop.item_id == itemId)
				{
					return drop.daily_max;
				}
			}
		}
		return 0;
	}
	
	private void loadActiveSql()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM character_drop_active"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					String accountName = rs.getString("account_name");
					int itemId = rs.getInt("item_id");
					long itemCount = rs.getLong("item_count");
					Map<Integer, Long> data = DATA_ACTIVE.computeIfAbsent(accountName, k -> new HashMap<>());
					data.put(itemId, itemCount);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void loadPassiveSql()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM character_drop_passive"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					String accountName = rs.getString("account_name");
					int itemId = rs.getInt("item_id");
					long itemCount = rs.getLong("item_count");
					Map<Integer, Long> data = DATA_PASSIVE.computeIfAbsent(accountName, k -> new HashMap<>());
					data.put(itemId, itemCount);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void saveActiveSql()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO `character_drop_active` (`account_name`, `item_id`, `item_count`) VALUES(?, ?, ?) ON DUPLICATE KEY UPDATE `item_count` = ?"))
		{
			for (Entry<String, Map<Integer, Long>> data : DATA_ACTIVE.entrySet())
			{
				for (Entry<Integer, Long> data2 : data.getValue().entrySet())
				{
					statement.setString(1, data.getKey());
					statement.setInt(2, data2.getKey());
					statement.setLong(3, data2.getValue());
					statement.setLong(4, data2.getValue());
					statement.addBatch();
				}
			}
			statement.executeBatch();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void savePassiveSql()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement("INSERT INTO `character_drop_passive` (`account_name`, `item_id`, `item_count`) VALUES(?, ?, ?) ON DUPLICATE KEY UPDATE `item_count` = ?"))
		{
			for (Entry<String, Map<Integer, Long>> data : DATA_PASSIVE.entrySet())
			{
				for (Entry<Integer, Long> data2 : data.getValue().entrySet())
				{
					statement.setString(1, data.getKey());
					statement.setInt(2, data2.getKey());
					statement.setLong(3, data2.getValue());
					statement.setLong(4, data2.getValue());
					statement.addBatch();
				}
			}
			statement.executeBatch();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	private void checkSchedule()
	{
		final LocalTime now = LocalTime.now().plusSeconds(1);
		if (now.isAfter(LocalTime.parse(BUFF_SCHEDULE.time_from)) && now.isBefore(LocalTime.parse(BUFF_SCHEDULE.time_to)))
		{
			BUFF_ACTIVE = false; // default true
		}
		else
		{
			BUFF_ACTIVE = false;
		}
		for (Schedule s : HOT_TIME_SCHEDULE)
		{
			if (now.isAfter(LocalTime.parse(s.time_from)) && now.isBefore(LocalTime.parse(s.time_to)))
			{
				DROP_BOOST = s.drop_boost;
				return;
			}
		}
		DROP_BOOST = 1.0;
	}
	
	public boolean isBuffActive()
	{
		return BUFF_ACTIVE;
	}
	
	public void dropItem(Player player, Monster monster)
	{
		if (monster.getLevel() + MAX_LEVEL_DIFFERENCE < player.getLevel())
		{
			player.sendMessage("Max level difference: " + MAX_LEVEL_DIFFERENCE);
			return;
		}
		boolean p = PremiumManager.getInstance().getPremiumExpiration(player.getAccountName()) > 0;
		for (Drop drop : DROP_LIST)
		{
			// double finalChance = p ? (drop.item_chance * 1.3) : drop.item_chance;
			// System.out.println("final Chance : " + finalChance);
			if (drop.item_chance > Rnd.get(100))
			{
				if (drop.premium && !p)
				{
					continue;
				}
				if (!drop.premium && p)
				{
					continue;
				}
				Map<Integer, Long> data = getActiveItemData(player.getAccountName(), drop.item_id);
				if (drop.daily_max <= data.get(drop.item_id))
				{
					continue;
				}
				long amount = Rnd.get(drop.item_min, drop.item_max);
				if (DROP_BOOST > 1.0)
				{
					amount *= DROP_BOOST;
				}
				if (amount + data.get(drop.item_id) > drop.daily_max)
				{
					amount = drop.daily_max - data.get(drop.item_id);
				}
				data.put(drop.item_id, data.get(drop.item_id) + amount);
				player.addItem("drop_manager", drop.item_id, amount, player, true);
			}
		}
	}
	
	public Map<Integer, Long> getActiveItemData(String accountName, int itemId)
	{
		Map<Integer, Long> data = DATA_ACTIVE.computeIfAbsent(accountName, k -> new HashMap<>());
		data.putIfAbsent(itemId, 0L);
		return data;
	}
	
	public Map<Integer, Long> getPassiveItemData(String accountName, int itemId)
	{
		Map<Integer, Long> data = DATA_PASSIVE.computeIfAbsent(accountName, k -> new HashMap<>());
		data.putIfAbsent(itemId, 0L);
		return data;
	}
	
	private String getDate()
	{
		return DATE_FORMATTER.format(new Date());
	}
	
	private class Schedule
	{
		public String	time_from;
		public String	time_to;
		public double	drop_boost;
		
		private Schedule(String time_from, String time_to, double drop_boost)
		{
			this.time_from = time_from;
			this.time_to = time_to;
			this.drop_boost = drop_boost;
		}
	}
	
	private class Drop
	{
		int		item_id;
		long	item_min;
		long	item_max;
		double	item_chance;
		int		daily_max;
		boolean	premium;
		
		Drop(int item_id, long item_min, long item_max, double item_chance, int daily_max, boolean premium)
		{
			this.item_id = item_id;
			this.item_min = item_min;
			this.item_max = item_max;
			this.item_chance = item_chance;
			this.daily_max = daily_max;
			this.premium = premium;
		}
	}
	
	private Map<Integer, ScheduledFuture<?>> _passive_income_drop = new HashMap<>();
	
	public void startPassiveDrop(Player player)
	{
		// Kiểm tra nếu người chơi không đủ điều kiện (không online hoặc đạt max drop), ngừng tạo task
		if (!player.isOnline() && !player.isOfflinePlay())
		{
			return;
		}
		if (player.isInOfflineMode())
		{
			return;
		}
		Map<Integer, Long> data = getPassiveItemData(player.getAccountName(), LCOIN);
		boolean isPremium = PremiumManager.getInstance().getPremiumExpiration(player.getAccountName()) > 0;
		long max = isPremium ? 13000 : 8000;
		// Nếu người chơi đã đạt max, không cần tạo thêm tác vụ
		if (data.getOrDefault(LCOIN, 0L) >= max)
		{
			return;
		}
		// Chỉ tạo task nếu chưa có task nào đang chạy cho người chơi này
		_passive_income_drop.computeIfAbsent(player.getObjectId(), k ->
		{
			return ThreadPool.scheduleAtFixedRate(new PassiveDrop(player), 35000, 35000);
		});
	}
	
	public void stopPassiveDrop(Player player)
	{
		ScheduledFuture<?> future = _passive_income_drop.remove(player.getObjectId());
		if (future != null)
		{
			future.cancel(true);
		}
	}
	
	public class PassiveDrop implements Runnable
	{
		private final Player _player;
		
		public PassiveDrop(Player player)
		{
			_player = player;
		}
		
		@Override
		public void run()
		{
			// Nếu người chơi không online và không ở chế độ chơi offline, dừng task
			if (!_player.isOnline() && !_player.isOfflinePlay())
			{
				stopPassiveDrop(_player);
				return;
			}
			// Kiểm tra điều kiện nếu người chơi đang ở chế độ offline, không cho nhận Lcoin
			if (_player.isInOfflineMode())
			{
				stopPassiveDrop(_player);
				return;
			}
			// Kiểm tra điều kiện thêm trước khi tiến hành logic passive drop
			boolean isPremium = PremiumManager.getInstance().getPremiumExpiration(_player.getAccountName()) > 0;
			Map<Integer, Long> data = DropManager.getInstance().getPassiveItemData(_player.getAccountName(), DropManager.LCOIN);
			long max = isPremium ? 13000 : 8000;
			// Nếu đã đạt giới hạn tối đa, dừng tác vụ
			if (data.getOrDefault(DropManager.LCOIN, 0L) >= max)
			{
				stopPassiveDrop(_player);
				return;
			}
			long amount = 13; // Số lượng LCoin thêm mỗi chu kỳ
			// Điều chỉnh số lượng để không vượt quá giới hạn tối đa
			if (amount + data.getOrDefault(DropManager.LCOIN, 0L) > max)
			{
				amount = max - data.getOrDefault(DropManager.LCOIN, 0L);
			}
			data.put(DropManager.LCOIN, data.getOrDefault(DropManager.LCOIN, 0L) + amount);
			_player.addItem("drop_manager", DropManager.LCOIN, amount, _player, true);
			// Nếu đạt giới hạn tối đa sau khi thêm, dừng tác vụ
			if (data.get(DropManager.LCOIN) >= max)
			{
				stopPassiveDrop(_player);
			}
		}
	}
	
	public static DropManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final DropManager INSTANCE = new DropManager();
	}
}