/*
 * Copyright (c) 2013 L2jMobius
 * 
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */
package org.l2jmobius.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.commons.util.Rnd;
import org.l2jmobius.gameserver.instancemanager.PremiumManager;
import org.l2jmobius.gameserver.model.actor.Player;

/**
 * Gift Code Manager
 * <AUTHOR>
 */
public class GiftCodeManager
{
	private static final Logger LOGGER = Logger.getLogger(GiftCodeManager.class.getName());
	
	// SQL Queries
	private static final String INSERT_GIFT_CODE = "INSERT INTO gift_codes (code) VALUES (?)";
	private static final String CHECK_GIFT_CODE = "SELECT * FROM gift_codes WHERE code = ?";
	private static final String USE_GIFT_CODE = "UPDATE gift_codes SET used = 1, used_by = ?, used_date = ? WHERE code = ? AND used = 0";
	private static final String DELETE_GIFT_CODE = "DELETE FROM gift_codes WHERE code = ?";
	private static final String CHECK_PLAYER_USAGE = "SELECT COUNT(*) FROM gift_codes WHERE used_by = ?";

	// Rate limiting - prevent spam attempts
	private final Map<Integer, Long> _lastAttemptTime = new ConcurrentHashMap<>();
	private static final long ATTEMPT_COOLDOWN = 10000; // 10 seconds between attempts
	
	// Gift code rewards
	private static final int[] REWARD_ITEMS = {94072, 94073, 49487};
	private static final int[] REWARD_COUNTS = {5, 5, 50};
	private static final int PREMIUM_DAYS = 7;
	
	protected GiftCodeManager()
	{
		LOGGER.info("GiftCodeManager: Initialized.");
		// Clean up old attempt data every hour
		ThreadPool.scheduleAtFixedRate(this::cleanupAttemptData, 3600000, 3600000);
	}

	/**
	 * Clean up old attempt data
	 */
	private void cleanupAttemptData()
	{
		final long currentTime = System.currentTimeMillis();
		_lastAttemptTime.entrySet().removeIf(entry -> (currentTime - entry.getValue()) > ATTEMPT_COOLDOWN * 60); // Keep for 10 minutes
	}
	
	/**
	 * Generate a random 6-character gift code
	 * @return the generated code
	 */
	public String generateGiftCode()
	{
		final String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
		final StringBuilder code = new StringBuilder();
		
		for (int i = 0; i < 6; i++)
		{
			code.append(chars.charAt(Rnd.get(chars.length())));
		}
		
		return code.toString();
	}
	
	/**
	 * Create a new gift code and store it in database
	 * @return the created gift code
	 */
	public String createGiftCode()
	{
		String code;
		boolean codeExists = true;
		
		// Generate unique code
		do
		{
			code = generateGiftCode();
			codeExists = checkCodeExists(code);
		}
		while (codeExists);
		
		// Store in database
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(INSERT_GIFT_CODE))
		{
			ps.setString(1, code);
			ps.executeUpdate();
			LOGGER.info("GiftCodeManager: Created new gift code: " + code);
			return code;
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error creating gift code: " + e.getMessage());
			return null;
		}
	}
	
	/**
	 * Check if a gift code exists in database
	 * @param code the code to check
	 * @return true if code exists
	 */
	private boolean checkCodeExists(String code)
	{
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(CHECK_GIFT_CODE))
		{
			ps.setString(1, code);
			try (ResultSet rs = ps.executeQuery())
			{
				return rs.next();
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error checking gift code: " + e.getMessage());
			return true; // Assume exists to be safe
		}
	}
	
	/**
	 * Use a gift code and give rewards to player
	 * @param player the player using the code
	 * @param code the gift code
	 * @return result message
	 */
	public synchronized String useGiftCode(Player player, String code)
	{
		if (code == null || code.length() != 6)
		{
			return "Mã gift code không hợp lệ!";
		}

		code = code.toUpperCase().trim();
		final int playerId = player.getObjectId();

		// Anti-spam check
		final long currentTime = System.currentTimeMillis();
		final Long lastAttempt = _lastAttemptTime.get(playerId);
		if (lastAttempt != null && (currentTime - lastAttempt) < ATTEMPT_COOLDOWN)
		{
			final long remainingTime = (ATTEMPT_COOLDOWN - (currentTime - lastAttempt)) / 1000;
			return "Vui lòng đợi " + remainingTime + " giây trước khi thử lại!";
		}

		// Update attempt time
		_lastAttemptTime.put(playerId, currentTime);

		// Check if player has already used a gift code (limit 1 per character)
		try (Connection con = DatabaseFactory.getConnection();
			PreparedStatement ps = con.prepareStatement(CHECK_PLAYER_USAGE))
		{
			ps.setInt(1, playerId);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next() && rs.getInt(1) > 0)
				{
					return "Bạn đã sử dụng gift code rồi! Mỗi nhân vật chỉ được sử dụng 1 gift code duy nhất.";
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error checking player usage: " + e.getMessage());
			return "Lỗi hệ thống, vui lòng thử lại!";
		}

		// Use atomic transaction to prevent race conditions
		try (Connection con = DatabaseFactory.getConnection())
		{
			con.setAutoCommit(false);

			// Check and mark code as used in one atomic operation
			try (PreparedStatement ps = con.prepareStatement(USE_GIFT_CODE))
			{
				ps.setInt(1, playerId);
				ps.setTimestamp(2, new Timestamp(currentTime));
				ps.setString(3, code);

				int updated = ps.executeUpdate();
				if (updated == 0)
				{
					con.rollback();
					return "Mã gift code không tồn tại hoặc đã được sử dụng!";
				}
			}

			// Give rewards
			boolean rewardSuccess = giveRewards(player);
			if (!rewardSuccess)
			{
				con.rollback();
				return "Lỗi khi trao phần thưởng! Vui lòng thử lại.";
			}

			// Delete the gift code after successful use
			try (PreparedStatement ps = con.prepareStatement(DELETE_GIFT_CODE))
			{
				ps.setString(1, code);
				ps.executeUpdate();
			}

			con.commit();

			LOGGER.info("GiftCodeManager: Player " + player.getName() + " used gift code: " + code);
			return "Chúc mừng! Bạn đã nhận được phần thưởng từ gift code!";
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error using gift code: " + e.getMessage());
			return "Lỗi hệ thống, vui lòng thử lại!";
		}
	}
	
	/**
	 * Give rewards to player
	 * @param player the player to give rewards
	 * @return true if successful
	 */
	private boolean giveRewards(Player player)
	{
		try
		{
			// Check if player has enough inventory space
			if (player.getInventory().getSize() >= (player.getInventoryLimit() - REWARD_ITEMS.length))
			{
				player.sendMessage("Inventory đầy! Vui lòng dọn dẹp inventory trước khi sử dụng gift code.");
				return false;
			}

			// Add premium time (7 days)
			PremiumManager.getInstance().addPremiumTime(player.getAccountName(), PREMIUM_DAYS, TimeUnit.DAYS);
			player.sendMessage("Bạn đã nhận được " + PREMIUM_DAYS + " ngày Premium!");

			// Add items
			for (int i = 0; i < REWARD_ITEMS.length; i++)
			{
				player.addItem("GiftCode", REWARD_ITEMS[i], REWARD_COUNTS[i], player, true);
			}

			player.sendMessage("Bạn đã nhận được các vật phẩm từ gift code!");
			return true;
		}
		catch (Exception e)
		{
			LOGGER.warning("GiftCodeManager: Error giving rewards to player " + player.getName() + ": " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * Show gift code statistics to admin
	 * @param admin the admin player
	 */
	public void showStats(Player admin)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Total codes available
			try (PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM gift_codes WHERE used = 0"))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					if (rs.next())
					{
						admin.sendMessage("=== GIFT CODE STATISTICS ===");
						admin.sendMessage("Available codes: " + rs.getInt(1));
					}
				}
			}

			// Total codes used today
			try (PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM gift_codes WHERE used = 1 AND DATE(used_date) = CURDATE()"))
			{
				try (ResultSet rs = ps.executeQuery())
				{
					if (rs.next())
					{
						admin.sendMessage("Codes used today: " + rs.getInt(1));
					}
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("GiftCodeManager: Error showing stats: " + e.getMessage());
			admin.sendMessage("Error retrieving gift code statistics.");
		}
	}

	public static GiftCodeManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}

	private static class SingletonHolder
	{
		protected static final GiftCodeManager INSTANCE = new GiftCodeManager();
	}
}
