/*
 * Copyright (C) 2004-2015 L2J Server
 * This file is part of L2J Server.
 * L2J Server is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * L2J Server is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.instancemanager;

import org.l2jmobius.gameserver.data.sql.CharInfoTable;
import org.l2jmobius.gameserver.model.Party;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.network.clientpackets.CharacterCreate;
import org.l2jmobius.gameserver.network.serverpackets.NpcHtmlMessage;
import org.l2jmobius.gameserver.network.serverpackets.PartySmallWindowAll;
import org.l2jmobius.gameserver.network.serverpackets.PartySmallWindowDeleteAll;
import org.l2jmobius.gameserver.util.BuilderUtil;
import org.l2jmobius.gameserver.util.Util;

public class NameChangerManager
{
	public static final NameChangerManager getInstance()
	{
		return SingletonHolder._instance;
	}
	
	protected NameChangerManager()
	{}
	
	public void showExchangingWindow(Player player)
	{
		if (player.getNameChangingItem() == -1)
		{
			return;
		}
		NpcHtmlMessage html = new NpcHtmlMessage();
		html.setFile(player, "data/html/custom/Donate/namechange.htm");
		html.replace("%dtn%", "");
		player.sendPacket(html);
	}
	
	public boolean setNameForPlayer(Player player, String bypass)
	{
		try
		{
			if (!bypass.startsWith("name_change "))
			{
				return false;
			}
			if (player.getNameChangingItem() == -1)
			{
				return false;
			}
			if (player.getInventory().getItemByObjectId(player.getNameChangingItem()) == null)
			{
				return false;
			}
			if (bypass.length() < 13)
			{
				return false;
			}
			final String _name = bypass.substring(12);
			String errorMsg = null;
			boolean proceed = true;
			if (_name.length() < 2)
			{
				errorMsg = "Names have to be at least 2 characters";
				proceed = false;
			}
			if (_name.length() > 23)
			{
				errorMsg = "Names cannot be longer than 23 characters";
				proceed = false;
			}
			if (!Util.isAlphaNumeric(_name) || !CharacterCreate.isValidName(_name))
			{
				errorMsg = "Invalid name";
				proceed = false;
			}
			if (CharInfoTable.getInstance().doesCharNameExist(_name))
			{
				if (!(player.getName().equalsIgnoreCase(_name) && !player.getName().equals(_name)))
				{
					errorMsg = "Name already exists";
					proceed = false;
				}
			}
			if (!proceed)
			{
				player.sendMessage(errorMsg);
				String filename = "data/html/custom/Donate/namechange.htm";
				NpcHtmlMessage itemReply = new NpcHtmlMessage(1);
				itemReply.setFile(player, filename);
				itemReply.replace("%dtn%", errorMsg);
				player.sendPacket(itemReply);
				return false;
			}
			// destroy item
			if (!player.destroyItem("Donation Name Change", player.getNameChangingItem(), 1, player, true))
			{
				return false;
			}
			player.setName(_name);
			CharInfoTable.getInstance().addName(player);
			player.storeMe();
			BuilderUtil.sendSysMessage(player, "Changed name to " + _name);
			player.broadcastUserInfo();
			player.setRaceChangingItem(-1);
			if (player.isInParty())
			{
				// Delete party window for other party members
				final Party party = player.getParty();
				party.broadcastToPartyMembers(player, PartySmallWindowDeleteAll.STATIC_PACKET);
				for (Player member : party.getMembers())
				{
					// And re-add
					if (member != player)
					{
						member.sendPacket(new PartySmallWindowAll(member, party));
					}
				}
			}
			if (player.getClan() != null)
			{
				player.getClan().broadcastClanStatus();
			}
		}
		catch (StringIndexOutOfBoundsException e)
		{ // Case of empty character name
			BuilderUtil.sendSysMessage(player, "Wrong Name");
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return true;
	}
	
	private static class SingletonHolder
	{
		protected static final NameChangerManager _instance = new NameChangerManager();
	}
}