package org.l2jmobius.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.gameserver.enums.MailType;
import org.l2jmobius.gameserver.model.Message;
import org.l2jmobius.gameserver.model.Party;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.itemcontainer.Mail;
import org.l2jmobius.gameserver.network.serverpackets.ExShowScreenMessage;

public class RaidCompletionManager
{
	// Map lưu thời gian hoàn thành của các party (ID của instance, thời gian hoàn thành)
	private static final long	DAILY_RESET_HOUR	= 6;	// 6:30 sáng
	private static final long	DAILY_RESET_MINUTE	= 30;
	
	public RaidCompletionManager()
	{
		scheduleSummaryTask();
	}
	
	// Khởi tạo tiến trình chạy lúc 6h30 sáng mỗi ngày
	private void scheduleSummaryTask()
	{
		// Lấy thời gian hiện tại và tính thời gian đến 6h30 sáng tiếp theo
		Calendar calendar = Calendar.getInstance();
		calendar.set(Calendar.HOUR_OF_DAY, (int) DAILY_RESET_HOUR);
		calendar.set(Calendar.MINUTE, (int) DAILY_RESET_MINUTE);
		calendar.set(Calendar.SECOND, 0);
		long delay = calendar.getTimeInMillis() - System.currentTimeMillis();
		if (delay < 0)
		{
			// Nếu thời gian hiện tại đã quá 6h30 sáng, chuyển đến ngày tiếp theo
			calendar.add(Calendar.DAY_OF_MONTH, 1);
			delay = calendar.getTimeInMillis() - System.currentTimeMillis();
		}
		// Schedule the summary task to run daily at 6:30 AM
		ThreadPool.scheduleAtFixedRate(this::generateDailySummary, delay, 24 * 60 * 60 * 1000); // Lặp lại mỗi 24 giờ
	}
	
	// Phương thức để lưu thời gian cho một party
	public void saveCompletionTime(int instanceId, Party party, long completionTime)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Lưu thông tin của party leader vào raid_completion_times
			String query = "INSERT INTO raid_completion_times (instance_id, party_leader_id, party_leader_name, completion_time) VALUES (?, ?, ?, ?)";
			try (PreparedStatement ps = con.prepareStatement(query))
			{
				Player leader = party.getLeader();
				ps.setInt(1, instanceId);
				ps.setInt(2, leader.getObjectId());
				ps.setString(3, leader.getName());
				ps.setLong(4, completionTime);
				ps.executeUpdate();
			}
			// Lưu thông tin của tất cả các thành viên vào raid_completion_members
			String memberQuery = "INSERT INTO raid_completion_members (instance_id, party_leader_id, member_id, member_name) VALUES (?, ?, ?, ?)";
			try (PreparedStatement ps = con.prepareStatement(memberQuery))
			{
				for (Player member : party.getMembers())
				{
					ps.setInt(1, instanceId);
					ps.setInt(2, party.getLeader().getObjectId());
					ps.setInt(3, member.getObjectId());
					ps.setString(4, member.getName());
					ps.executeUpdate();
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	// Phương thức để lưu thời gian cho một người chơi solo
	public void saveCompletionTime(int instanceId, Player player, long completionTime)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("INSERT INTO raid_completion_times (instance_id, party_leader_id, party_leader_name, completion_time) VALUES (?, ?, ?, ?)"))
		{
			ps.setInt(1, instanceId);
			ps.setInt(2, player.getObjectId());
			ps.setString(3, player.getName());
			ps.setLong(4, completionTime);
			ps.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	// Tổng kết danh sách top 1-2-3 party vào 6h30 sáng
	public void generateDailySummary()
	{
		String query = """
		    SELECT
		        rc.instance_id,
		        rc.party_leader_id,
		        rc.completion_time,
		        rcm.member_id,
		        rcm.member_name
		    FROM
		        raid_completion_times rc
		    JOIN
		        raid_completion_members rcm
		    ON
		        rc.instance_id = rcm.instance_id
		    WHERE
		        DATE(rc.completion_date) = CURDATE()
		    ORDER BY
		        rc.completion_time ASC
		    LIMIT 3
		""";
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement(query); ResultSet rs = ps.executeQuery())
		{
			int rank = 1;
			StringBuilder topPartiesMessage = new StringBuilder("Today's Top Parties:\n");
			// Kiểm tra nếu không có bản ghi nào trong ResultSet
			if (!rs.isBeforeFirst())
			{
				topPartiesMessage.append("No parties completed a raid today.\n");
			}
			else
			{
				// Duyệt qua các bản ghi nếu có
				while (rs.next())
				{
					String memberName = rs.getString("member_name");
					long completionTime = rs.getLong("completion_time");
					int memberId = rs.getInt("member_id");
					long hours = (completionTime / 3600000);
					long minutes = (completionTime / 60000) % 60;
					long seconds = (completionTime / 1000) % 60;
					topPartiesMessage.append(String.format("Rank %d: %s - Time: %02d:%02d:%02d\n", rank, memberName, hours, minutes, seconds));
					// Phát thưởng
					Player member = World.getInstance().getPlayer(memberId);
					if (member != null)
					{
						sendRewardMail(member, rank);
					}
					else
					{
						sendOfflineRewardMail(memberId, memberName, rank);
					}
					rank++;
				}
			}
			// Hiển thị danh sách top cho toàn bộ người chơi
			broadcastTopParties(topPartiesMessage.toString());
			// Lưu dữ liệu vào lịch sử và xóa dữ liệu tạm
			archiveRaidData();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public String getRaidHistory(int playerId)
	{
		StringBuilder message = new StringBuilder();
		// Bắt đầu HTML và bảng
		message.append("<html><body>");
		message.append("<center>Raid Time History</center>");
		message.append("<table width=320 border=1 cellspacing=0 cellpadding=4>");
		// Hàng đầu tiên với tiêu đề
		message.append("<tr bgcolor='#1E1E1E'>");
		message.append("<td width='20%' align='center'><font color='#FFFFFF'>Date</font></td>");
		message.append("<td width='40%' align='center'><font color='#FFFFFF'>Time</font></td>");
		message.append("<td width='40%' align='center'><font color='#FFFFFF'>Leader</font></td>");
		message.append("</tr>");
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT completion_date, completion_time, party_leader_name " + "FROM raid_history WHERE player_id = ? ORDER BY completion_date DESC"))
		{
			ps.setInt(1, playerId);
			try (ResultSet rs = ps.executeQuery())
			{
				boolean hasData = false;
				// Duyệt qua từng hàng dữ liệu và thêm vào bảng
				while (rs.next())
				{
					hasData = true;
					String date = rs.getString("completion_date");
					long completionTime = rs.getLong("completion_time");
					String leaderName = rs.getString("party_leader_name");
					// Định dạng thời gian thành HH:MM:SS
					long hours = (completionTime / 3600000);
					long minutes = (completionTime / 60000) % 60;
					long seconds = (completionTime / 1000) % 60;
					String formattedTime = String.format("%02d:%02d:%02d", hours, minutes, seconds);
					// Thêm hàng dữ liệu vào bảng
					message.append("<tr>");
					message.append("<td align='center'>").append(date).append("</td>");
					message.append("<td align='center'>").append(formattedTime).append("</td>");
					message.append("<td align='center'>").append(leaderName).append("</td>");
					message.append("</tr>");
				}
				// Nếu không có dữ liệu, hiển thị thông báo
				if (!hasData)
				{
					message.append("<tr>");
					message.append("<td colspan='3' align='center'>No Raid History Available</td>");
					message.append("</tr>");
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		// Kết thúc bảng và HTML
		message.append("</table>");
		message.append("</body></html>");
		return message.toString();
	}
	
	// Phương thức xóa dữ liệu raid sau khi phát thưởng
	private void clearRaidData()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps1 = con.prepareStatement("DELETE FROM raid_completion_members"); PreparedStatement ps2 = con.prepareStatement("DELETE FROM raid_completion_times"))
		{
			ps1.executeUpdate();
			ps2.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	// Hiển thị danh sách top cho người chơi
	private void broadcastTopParties(String message)
	{
		for (Player player : World.getInstance().getPlayers())
		{
			if (player != null && player.isOnline())
			{
				ExShowScreenMessage screenMessage = new ExShowScreenMessage(message, ExShowScreenMessage.TOP_CENTER, 10000);
				player.sendPacket(screenMessage);
			}
		}
	}
	
	// Trao phần thưởng cho các top party dựa trên thứ hạng
	private void rewardTopParties()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT rc.instance_id, rc.party_leader_id, rcm.member_id, rcm.member_name " + "FROM raid_completion_times rc " + "JOIN raid_completion_members rcm ON rc.instance_id = rcm.instance_id " + "WHERE DATE(rc.completion_date) = CURDATE() " + "ORDER BY rc.completion_time ASC LIMIT 3"))
		{
			int rank = 1;
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					int memberId = rs.getInt("member_id");
					String memberName = rs.getString("member_name");
					// Tìm người chơi online thông qua ID
					Player member = World.getInstance().getPlayer(memberId);
					if (member != null)
					{
						sendRewardMail(member, rank); // Gửi thưởng cho người chơi online
					}
					else
					{
						sendOfflineRewardMail(memberId, memberName, rank); // Gửi thưởng cho người chơi offline
					}
				}
				rank++;
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	// Phương thức gửi thư cho người chơi online
	private void sendRewardMail(Player member, int rank)
	{
		String mailTitle = "Phần thưởng Raid - Hạng " + rank;
		String mailBody = "Chúc mừng bạn đã đạt hạng " + rank + " trong sự kiện Raid. Đây là phần thưởng của bạn!";
		Message mail = new Message(member.getObjectId(), mailTitle, mailBody, MailType.TOP_RAID_FRINTEZZA);
		Mail attachment = mail.createAttachments();
		int rewardItemId = getRewardItemId(rank);
		int rewardAmount = getRewardAmount(rank);
		attachment.addItem("Raid Reward", rewardItemId, rewardAmount, member, this);
		MailManager.getInstance().sendMessage(mail);
		member.sendMessage("Bạn đã nhận được phần thưởng qua thư cho hạng " + rank + " trong sự kiện raid.");
	}
	
	private void sendOfflineRewardMail(int memberId, String memberName, int rank)
	{
		String mailTitle = "Phần thưởng Raid - Hạng " + rank;
		String mailBody = "Chúc mừng bạn đã đạt hạng " + rank + " trong sự kiện Raid. Đây là phần thưởng của bạn!";
		Message mail = new Message(memberId, mailTitle, mailBody, MailType.TOP_RAID_FRINTEZZA);
		Mail attachment = mail.createAttachments();
		int rewardItemId = getRewardItemId(rank);
		int rewardAmount = getRewardAmount(rank);
		attachment.addItem("Raid Reward", rewardItemId, rewardAmount, null, this);
		MailManager.getInstance().sendMessage(mail);
	}
	
	// Lấy ID phần thưởng dựa trên hạng
	private int getRewardItemId(int rank)
	{
		return (rank == 1) ? 57 : (rank == 2) ? 57 : 57; // Ví dụ: 57 = Adena, 4037 = Item khác, 4041 = Item khác
	}
	
	// Lấy số lượng phần thưởng dựa trên hạng
	private int getRewardAmount(int rank)
	{
		return (rank == 1) ? 10000 : (rank == 2) ? 5000 : 3000;
	}
	
	public String getTopRaidsMessage()
	{
		StringBuilder message = new StringBuilder();
		// Mở thẻ HTML và thiết lập bảng
		// message.append("<html><center><body>");
		message.append("<table width=320 border=1 cellspacing=0 cellpadding=4>");
		// Tiêu đề bảng
		message.append("<tr bgcolor='#1E1E1E'>"); // Nền tối cho tiêu đề
		message.append("<td width='20%' align='center'><b><font color='#FFFFFF'>Rank</font></b></td>");
		message.append("<td width='40%' align='center'><b><font color='#FFFFFF'>Party Name</font></b></td>");
		message.append("<td width='40%' align='center'><b><font color='#FFFFFF'>Raid Time</font></b></td>");
		message.append("</tr>");
		// Lấy danh sách các top party được lưu trữ
		List<RaidCompletionData> topParties = getTopParties();
		int rank = 1;
		// Duyệt qua từng party trong danh sách và hiển thị theo thứ hạng
		for (RaidCompletionData data : topParties)
		{
			// Xác định màu nền theo thứ hạng
			String bgcolor;
			switch (rank)
			{
				case 1:
					bgcolor = "#FFD700"; // Vàng cho hạng 1
					break;
				case 2:
					bgcolor = "#C0C0C0"; // Bạc cho hạng 2
					break;
				case 3:
					bgcolor = "#CD7F32"; // Đồng cho hạng 3
					break;
				default:
					bgcolor = "#FFFFFF"; // Mặc định là trắng cho các hạng khác
					break;
			}
			// Thêm hàng dữ liệu cho từng party với nền màu
			message.append("<tr bgcolor='").append(bgcolor).append("'>");
			message.append("<td align='center'>").append(rank).append("</td>");
			message.append("<td align='center'>").append(data.getPartyName()).append("</td>");
			message.append("<td align='center'>").append(data.getFormattedTime()).append("</td>");
			message.append("</tr>");
			rank++;
		}
		// Đóng bảng và HTML
		message.append("</table>");
		// message.append("</body></html>");
		return message.toString();
	}
	
	public List<RaidCompletionData> getTopParties()
	{
		List<RaidCompletionData> topParties = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT party_leader_name, completion_time FROM raid_completion_times WHERE DATE(completion_date) = CURDATE() ORDER BY completion_time ASC LIMIT 3"))
		{
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					String leaderName = rs.getString("party_leader_name");
					long completionTime = rs.getLong("completion_time");
					topParties.add(new RaidCompletionData(leaderName, completionTime));
				}
			}
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
		return topParties;
	}
	
	public void archiveRaidData()
	{
		String insertHistorySQL = """
		    INSERT INTO raid_history
		    (player_id, player_name, completion_date, completion_time, instance_id, party_leader_name)
		    SELECT
		        rcm.member_id,
		        rcm.member_name,
		        rc.completion_date,
		        rc.completion_time,
		        rc.instance_id,
		        rc.party_leader_name
		    FROM
		        raid_completion_times rc
		    JOIN
		        raid_completion_members rcm
		    ON
		        rc.instance_id = rcm.instance_id
		""";
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement psInsert = con.prepareStatement(insertHistorySQL); PreparedStatement psDeleteCompletion = con.prepareStatement("DELETE FROM raid_completion_times"); PreparedStatement psDeleteMembers = con.prepareStatement("DELETE FROM raid_completion_members"))
		{
			// Chuyển dữ liệu vào bảng lịch sử
			psInsert.executeUpdate();
			// Xóa dữ liệu tạm sau khi chuyển
			psDeleteCompletion.executeUpdate();
			psDeleteMembers.executeUpdate();
		}
		catch (SQLException e)
		{
			e.printStackTrace();
		}
	}
	
	public class RaidCompletionData
	{
		private final String	partyName;
		private final long		completionTime;
		
		public RaidCompletionData(String partyName, long completionTime)
		{
			this.partyName = partyName;
			this.completionTime = completionTime;
		}
		
		public String getPartyName()
		{
			return partyName;
		}
		
		public String getFormattedTime()
		{
			long seconds = (completionTime / 1000) % 60;
			long minutes = (completionTime / (1000 * 60)) % 60;
			long hours = (completionTime / (1000 * 60 * 60)) % 24;
			return String.format("%02d:%02d:%02d", hours, minutes, seconds);
		}
	}
	
	// Singleton
	public static RaidCompletionManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final RaidCompletionManager INSTANCE = new RaidCompletionManager();
	}
}
