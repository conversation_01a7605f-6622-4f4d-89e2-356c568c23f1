package org.l2jmobius.gameserver.instancemanager;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.util.Rnd;
import org.l2jmobius.gameserver.cache.HtmCache;
import org.l2jmobius.gameserver.data.xml.ItemData;
import org.l2jmobius.gameserver.data.xml.ReferralRewardData;
import org.l2jmobius.gameserver.enums.ChatType;
import org.l2jmobius.gameserver.handler.CommunityBoardHandler;
import org.l2jmobius.gameserver.model.World;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.events.Containers;
import org.l2jmobius.gameserver.model.events.EventType;
import org.l2jmobius.gameserver.model.events.impl.creature.player.OnPlayerLevelChanged;
import org.l2jmobius.gameserver.model.events.impl.creature.player.OnPlayerLogin;
import org.l2jmobius.gameserver.model.events.impl.creature.player.OnPlayerPvPChanged;
import org.l2jmobius.gameserver.model.events.listeners.ConsumerEventListener;
import org.l2jmobius.gameserver.model.holders.ItemHolder;
import org.l2jmobius.gameserver.network.serverpackets.CreatureSay;
import org.l2jmobius.gameserver.network.serverpackets.ExShowScreenMessage;

public class ReferralManager
{
	private static final Logger			LOGGER				= Logger.getLogger(ReferralManager.class.getName());
	private final Map<String, Integer>	referralCodes		= new HashMap<>();									// Lưu mã referral và ID người chơi
	private final Map<Integer, String>	referralCodeCache	= new HashMap<>();									// Cache mã referral
	private static final int			PLAYERS_PER_PAGE	= 5;
	
	private ReferralManager()
	{
		// Listener khi người chơi login
		Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_LOGIN, (OnPlayerLogin event) ->
		{
			Player player = event.getPlayer();
			if (!player.getVariables().getBoolean("hasReferralCode", false))
			{
				// Tạo mã referral nếu chưa có
				String referralCode = generateReferralCode(player);
				player.getVariables().set("hasReferralCode", true);
				player.getVariables().set("referral_code", referralCode);
				player.getVariables().storeMe(); // Lưu vào database
				player.sendMessage("Your referral code: " + referralCode);
			}
		}, this));
		// Listener khi người chơi lên cấp
		Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_LEVEL_CHANGED, (OnPlayerLevelChanged event) -> onPlayerLevelChanged(event), this));
		// Listener khi PvP thay đổi
		Containers.Players().addListener(new ConsumerEventListener(Containers.Players(), EventType.ON_PLAYER_PVP_CHANGED, (OnPlayerPvPChanged event) -> onPlayerPvPChanged(event), this));
	}
	
	// Hiển thị thông tin referral qua Community Board
	public void showReferralPageInCommunityBoard(Player player, int currentPage)
	{
		// Tải nội dung HTML từ file
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/referralSystem/home.html");
		if (html == null)
		{
			player.sendMessage("Could not load the referral page.");
			return;
		}
		// Lấy mã referral từ cơ sở dữ liệu và thay thế trong HTML
		String referralCode = getReferralCodeFromDatabase(player);
		if (referralCode == null || referralCode.isEmpty())
		{
			referralCode = "N/A"; // Giá trị mặc định nếu không có mã referral
		}
		html = html.replace("%referral_code%", referralCode);
		// Tạo nội dung cho phần thưởng của người giới thiệu và người được mời
		String referrerRewardText = buildRewardText(getReferrerRewards(player), new HashSet<>());
		String referredRewardText = buildRewardText(getReferredRewards(player), new HashSet<>());
		// Thay thế phần thưởng của người giới thiệu và người được mời trong HTML
		html = html.replace("%referrer_rewards%", referrerRewardText);
		html = html.replace("%referred_rewards%", referredRewardText);
		// Lấy danh sách tất cả người chơi được mời
		List<Integer> invitedPlayerIds = getInvitedPlayerIds(player);
		int totalPlayers = invitedPlayerIds.size();
		// Tính tổng số trang
		int totalPages = (int) Math.ceil((double) totalPlayers / PLAYERS_PER_PAGE);
		// Giới hạn trang hiện tại trong khoảng hợp lệ
		if (currentPage > totalPages)
		{
			currentPage = totalPages;
		}
		if (currentPage < 1)
		{
			currentPage = 1;
		}
		// Tính toán vị trí bắt đầu và kết thúc cho trang hiện tại
		int start = (currentPage - 1) * PLAYERS_PER_PAGE;
		int end = Math.min(start + PLAYERS_PER_PAGE, totalPlayers);
		// Xây dựng HTML cho danh sách người chơi được mời
		StringBuilder invitedPlayersHtml = new StringBuilder();
		for (int i = start; i < end; i++)
		{
			int invitedPlayerId = invitedPlayerIds.get(i);
			Player invitedPlayer = getOnlinePlayer(invitedPlayerId);
			PlayerData invitedPlayerData = null;
			String invitedPlayerName;
			if (invitedPlayer != null)
			{
				invitedPlayerName = invitedPlayer.getName();
			}
			else
			{
				invitedPlayerData = getOfflinePlayerData(invitedPlayerId);
				if (invitedPlayerData != null)
				{
					invitedPlayerName = invitedPlayerData.getName();
				}
				else
				{
					continue; // Bỏ qua nếu không lấy được dữ liệu người chơi
				}
			}
			// Xây dựng HTML cho từng người chơi
			invitedPlayersHtml.append("<tr>");
			invitedPlayersHtml.append("<td>").append(invitedPlayerName).append("</td>");
			invitedPlayersHtml.append("<td><button action=\"bypass _bbsreferral_view_activity ").append(invitedPlayerId).append("\" value=\"View History\" width=100 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"/></td>");
			invitedPlayersHtml.append("</tr>");
		}
		html = html.replace("%invited_players%", invitedPlayersHtml.toString());
		// Thay thế số trang hiện tại và tổng số trang
		html = html.replace("%current_page%", String.valueOf(currentPage));
		html = html.replace("%total_pages%", String.valueOf(totalPages));
		html = html.replace("%invited_count%", String.valueOf(totalPlayers));
		// Tạo HTML cho phân trang
		StringBuilder paginationHtml = new StringBuilder();
		if (currentPage > 1)
		{
			paginationHtml.append("<button action=\"bypass _bbsreferral_page ").append(currentPage - 1).append("\" value=\"Previous\" width=100 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"/>");
		}
		if (currentPage < totalPages)
		{
			paginationHtml.append("<button action=\"bypass _bbsreferral_page ").append(currentPage + 1).append("\" value=\"Next\" width=100 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"/>");
		}
		// Thay thế phần phân trang trong HTML
		html = html.replace("%pagination%", paginationHtml.toString());
		// Kiểm tra xem người chơi đã nhập mã giới thiệu hay chưa (người được mời)
		if (getReferrerId(player) != 0)
		{
			String referrerName = getReferrerName(player);
			// Thêm nút "Cancel Referrer" nếu người chơi đã nhập mã giới thiệu
			html = html.replace("%cancel_button%", "<button action=\"bypass _bbsreferral_cancel_referral\" value=\"Cancel Referrer (" + referrerName + ")\" width=150 height=25 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"/>");
		}
		else
		{
			// Xóa nút "Cancel Referrer" nếu không có mã giới thiệu
			html = html.replace("%cancel_button%", "");
		}
		// Gửi HTML đến người chơi
		CommunityBoardHandler.separateAndSend(html, player);
	}
	
	public String buildRewardText(List<ItemHolder> rewards, Set<String> addedRewards)
	{
		if (rewards == null || rewards.isEmpty())
		{
			return "<table width=200><tr><td width=32><img src=\"icon.etc_question_mark_i00\" width=32 height=32></td><td>No rewards available</td></tr></table>";
		}
		StringBuilder rewardText = new StringBuilder();
		for (ItemHolder reward : rewards)
		{
			String rewardKey = reward.getId() + ":" + reward.getCount(); // Xác định duy nhất phần thưởng
			if (!addedRewards.contains(rewardKey)) // Kiểm tra nếu phần thưởng chưa được thêm
			{
				addedRewards.add(rewardKey); // Thêm vào danh sách phần thưởng đã được thêm
				String itemName = ItemData.getInstance().getTemplate(reward.getId()).getName();
				int itemCount = (int) reward.getCount();
				String itemIcon = ItemData.getInstance().getTemplate(reward.getId()).getIcon();
				rewardText.append("<table width=200><tr>").append("<td width=32 align=center><img src=\"").append(itemIcon).append("\" width=32 height=32></td>").append("<td align=center>").append(itemName).append(" x ").append(itemCount).append("</td>").append("</tr></table>");
			}
		}
		return rewardText.toString();
	}
	
	// Xử lý sự kiện khi người chơi lên cấp (Yêu cầu 5)
	private void onPlayerLevelChanged(OnPlayerLevelChanged event)
	{
		final Player player = event.getPlayer();
		final int playerLevel = player.getLevel();
		// Kiểm tra xem người chơi có referrer không
		int referrerId = getReferrerId(player);
		if (referrerId != 0)
		{
			Player referrer = getOnlinePlayer(referrerId);
			PlayerData referrerData = null;
			if (referrer == null)
			{
				referrerData = getOfflinePlayerData(referrerId);
			}
			if (referrer != null || referrerData != null)
			{
				List<ItemHolder> referredRewards = ReferralRewardData.getInstance().getRewardsByCondition("level", playerLevel);
				if (referredRewards != null && !referredRewards.isEmpty())
				{
					giveReward(player, referredRewards); // Trao phần thưởng cho người được mời
				}
			}
		}
	}
	
	// Xử lý sự kiện khi điểm PvP thay đổi (Yêu cầu 5)
	private void onPlayerPvPChanged(OnPlayerPvPChanged event)
	{
		Player player = event.getPlayer();
		int pvpCount = event.getNewPoints();
		// Kiểm tra xem người chơi có referrer không
		int referrerId = getReferrerId(player);
		if (referrerId != 0)
		{
			Player referrer = getOnlinePlayer(referrerId);
			PlayerData referrerData = null;
			if (referrer == null)
			{
				referrerData = getOfflinePlayerData(referrerId);
			}
			if (referrer != null || referrerData != null)
			{
				// Người được mời (player) đạt mốc PvP quan trọng
				List<ItemHolder> referredRewards = ReferralRewardData.getInstance().getRewardsByCondition("pvp", pvpCount);
				if (referredRewards != null && !referredRewards.isEmpty())
				{
					giveReward(player, referredRewards); // Trao phần thưởng cho người được mời
				}
				// Người giới thiệu sẽ tự nhận thưởng qua HTML
			}
		}
	}
	
	public void claimReferrerRewards(Player referrer, int invitedPlayerId)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Kiểm tra cấp độ và PvP của người nhập mã đã được claim lần cuối
			PreparedStatement psCheck = con.prepareStatement("SELECT last_claimed_level, last_claimed_pvp FROM referrals WHERE referred_id = ? AND referrer_id = ?");
			psCheck.setInt(1, invitedPlayerId);
			psCheck.setInt(2, referrer.getObjectId());
			ResultSet rsCheck = psCheck.executeQuery();
			int lastClaimedLevel = 0;
			int lastClaimedPvp = 0;
			if (rsCheck.next())
			{
				lastClaimedLevel = rsCheck.getInt("last_claimed_level");
				lastClaimedPvp = rsCheck.getInt("last_claimed_pvp");
			}
			// Kiểm tra người chơi online hoặc offline
			Player invitedPlayer = getPlayerById(invitedPlayerId); // Lấy thông tin người chơi online
			PlayerData invitedPlayerData = null;
			int currentLevel;
			int currentPvpKills;
			if (invitedPlayer == null)
			{
				// Nếu người chơi offline, lấy thông tin từ cơ sở dữ liệu
				invitedPlayerData = getOfflinePlayerData(invitedPlayerId);
				if (invitedPlayerData == null)
				{
					referrer.sendMessage("The invited player does not exist.");
					return;
				}
				currentLevel = invitedPlayerData.getLevel();
				currentPvpKills = invitedPlayerData.getPvpKills();
			}
			else
			{
				currentLevel = invitedPlayer.getLevel();
				currentPvpKills = invitedPlayer.getPvpKills();
			}
			// Danh sách phần thưởng đã thêm để tránh nhân đôi
			Set<String> addedRewards = new HashSet<>();
			List<ItemHolder> finalRewards = new ArrayList<>();
			// Chỉ thêm phần thưởng dựa trên level nếu level thay đổi
			if (currentLevel > lastClaimedLevel)
			{
				List<ItemHolder> referrerRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_level", currentLevel);
				for (ItemHolder reward : referrerRewards)
				{
					String rewardKey = reward.getId() + ":" + reward.getCount();
					if (!addedRewards.contains(rewardKey))
					{
						finalRewards.add(reward);
						addedRewards.add(rewardKey);
					}
				}
			}
			// Chỉ thêm phần thưởng dựa trên PvP nếu PvP thay đổi
			if (currentPvpKills > lastClaimedPvp)
			{
				List<ItemHolder> pvpRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_pvp", currentPvpKills);
				for (ItemHolder reward : pvpRewards)
				{
					String rewardKey = reward.getId() + ":" + reward.getCount();
					if (!addedRewards.contains(rewardKey))
					{
						finalRewards.add(reward);
						addedRewards.add(rewardKey);
					}
				}
			}
			// Nếu có phần thưởng mới, trao cho người giới thiệu
			if (!finalRewards.isEmpty())
			{
				giveReward(referrer, finalRewards);
				// Cập nhật cấp độ và PvP đã claim vào CSDL
				PreparedStatement psUpdate = con.prepareStatement("UPDATE referrals SET last_claimed_level = ?, last_claimed_pvp = ? WHERE referred_id = ? AND referrer_id = ?");
				psUpdate.setInt(1, currentLevel);
				psUpdate.setInt(2, currentPvpKills);
				psUpdate.setInt(3, invitedPlayerId);
				psUpdate.setInt(4, referrer.getObjectId());
				psUpdate.executeUpdate();
				referrer.sendMessage("You have successfully claimed your referrer rewards for invited player: " + (invitedPlayer != null ? invitedPlayer.getName() : invitedPlayerData.getName()));
			}
			else
			{
				referrer.sendMessage("There are no new rewards available to claim for this player.");
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	// Tạo mã referral theo yêu cầu 1
	public String generateReferralCode(Player player)
	{
		// Kiểm tra cache có mã referral của người chơi không
		if (referralCodeCache.containsKey(player.getObjectId()))
		{
			return referralCodeCache.get(player.getObjectId());
		}
		// Tạo mã mới và lưu vào cache và database
		String code = player.getName().toUpperCase() + "_" + Rnd.get(1000, 9999);
		referralCodes.put(code, player.getObjectId());
		referralCodeCache.put(player.getObjectId(), code);
		saveReferralCodeToDatabase(player, code);
		return code;
	}
	
	// Lưu mã referral vào CSDL
	private void saveReferralCodeToDatabase(Player player, String code)
	{
		try (Connection con = DatabaseFactory.getConnection())
		{
			PreparedStatement psCheck = con.prepareStatement("SELECT COUNT(*) FROM referral_codes WHERE player_id = ?");
			psCheck.setInt(1, player.getObjectId());
			ResultSet rs = psCheck.executeQuery();
			if (rs.next() && rs.getInt(1) > 0)
			{
				PreparedStatement psUpdate = con.prepareStatement("UPDATE referral_codes SET referral_code = ? WHERE player_id = ?");
				psUpdate.setString(1, code);
				psUpdate.setInt(2, player.getObjectId());
				psUpdate.executeUpdate();
			}
			else
			{
				PreparedStatement psInsert = con.prepareStatement("INSERT INTO referral_codes (player_id, referral_code) VALUES (?, ?)");
				psInsert.setInt(1, player.getObjectId());
				psInsert.setString(2, code);
				psInsert.executeUpdate();
			}
		}
		catch (Exception e)
		{
			LOGGER.severe("Could not save referral code for player " + player.getName() + ": " + e.getMessage());
		}
	}
	
	// Trao phần thưởng cho người chơi
	private void giveReward(Player player, List<ItemHolder> rewards)
	{
		for (ItemHolder reward : rewards)
		{
			player.addItem("Referral Reward", reward.getId(), reward.getCount(), player, true);
		}
	}
	
	// Lấy ID người giới thiệu từ CSDL
	private int getReferrerId(Player player)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT referrer_id FROM referrals WHERE referred_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					int referrerId = rs.getInt("referrer_id");
					return referrerId;
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return 0; // Trả về 0 nếu không tìm thấy referrer_id
	}
	
	// Cập nhật tiến trình của người được mời
	public void updateReferredProgress(Player referred, int level, int pvpPoints)
	{
		int referrerId = getReferrerId(referred);
		if (referrerId == 0)
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			PreparedStatement ps = con.prepareStatement("REPLACE INTO referral_progress (referred_id, referrer_id, current_level, pvp_points) VALUES (?, ?, ?, ?)");
			ps.setInt(1, referred.getObjectId());
			ps.setInt(2, referrerId); // Lấy ID của người giới thiệu
			ps.setInt(3, level); // Cấp độ hiện tại
			ps.setInt(4, pvpPoints); // Điểm PvP hiện tại
			ps.executeUpdate();
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
	}
	
	// Lấy đối tượng Player từ World dựa trên ID của người chơi
	private Player getPlayerById(int playerId)
	{
		return World.getInstance().getPlayer(playerId);
	}
	
	// Lấy mã referral từ cơ sở dữ liệu dựa trên ID người chơi
	public String getReferralCodeFromDatabase(Player player)
	{
		if (referralCodeCache.containsKey(player.getObjectId()))
		{
			return referralCodeCache.get(player.getObjectId());
		}
		String referralCode = null;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT referral_code FROM referral_codes WHERE player_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					referralCode = rs.getString("referral_code");
					referralCodeCache.put(player.getObjectId(), referralCode); // Lưu cache mã referral
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		if (referralCode == null)
		{
			referralCode = generateReferralCode(player); // Tạo mã referral nếu không tồn tại
		}
		return referralCode;
	}
	
	// Đếm số người chơi đã nhập mã referral của người chơi hiện tại
	public int getInvitedCount(Player player)
	{
		int invitedCount = 0;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM referrals WHERE referrer_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					invitedCount = rs.getInt(1);
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return invitedCount;
	}
	
	public boolean referralCodeAlreadyUsed(Player player, String enteredCode)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM referrals WHERE referred_id = ?"))
		{
			ps.setInt(1, player.getObjectId());
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next() && rs.getInt(1) > 0)
				{
					return true;
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return false;
	}
	
	public int getReferrerIdByCode(String code)
	{
		int referrerId = 0;
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT player_id FROM referral_codes WHERE referral_code = ?"))
		{
			ps.setString(1, code);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					referrerId = rs.getInt("player_id");
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return referrerId;
	}
	
	public boolean hasMutualReferral(int playerId, int referrerId)
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT COUNT(*) FROM referrals WHERE (referrer_id = ? AND referred_id = ?) OR (referrer_id = ? AND referred_id = ?)"))
		{
			ps.setInt(1, referrerId);
			ps.setInt(2, playerId);
			ps.setInt(3, playerId);
			ps.setInt(4, referrerId);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next() && rs.getInt(1) > 0)
				{
					return true;
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return false;
	}
	
	public void saveReferral(Player referrer, Player referred)
	{
		if (referralCodeAlreadyUsed(referred, null))
		{
			referrer.sendMessage(referred.getName() + " has already used a referral code.");
			return;
		}
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Thêm bản ghi referral vào bảng referrals
			PreparedStatement psInsert = con.prepareStatement("INSERT INTO referrals (referrer_id, referred_id, referred_level) VALUES (?, ?, ?)");
			psInsert.setInt(1, referrer.getObjectId());
			psInsert.setInt(2, referred.getObjectId());
			psInsert.setInt(3, referred.getLevel());
			psInsert.executeUpdate();
		}
		catch (Exception e)
		{
			System.out.println("Could not save referral: " + e.getMessage());
		}
	}
	
	public boolean processReferralCode(Player player, String enteredCode)
	{
		if (enteredCode == null || enteredCode.isEmpty())
		{
			player.sendMessage("You must enter a referral code.");
			return false;
		}
		// Kiểm tra xem người chơi đã sử dụng mã referral chưa
		if (referralCodeAlreadyUsed(player, enteredCode))
		{
			player.sendMessage("You have already used a referral code.");
			return false;
		}
		// Kiểm tra mã referral có tồn tại không
		int referrerId = getReferrerIdByCode(enteredCode);
		if (referrerId == 0)
		{
			player.sendMessage("Invalid referral code.");
			return false;
		}
		// Người chơi không thể sử dụng mã của chính mình
		if (referrerId == player.getObjectId())
		{
			player.sendMessage("You cannot use your own referral code.");
			return false;
		}
		// Kiểm tra mã mời đối nghịch
		if (hasMutualReferral(player.getObjectId(), referrerId))
		{
			player.sendMessage("You cannot enter the referral code of someone who has used your referral code.");
			return false;
		}
		// Kiểm tra xem người chơi đã có một referrer chưa, nếu có thì yêu cầu hủy trước
		if (getReferrerId(player) != 0)
		{
			player.sendMessage("You already have a referrer. Please cancel the current referral connection before entering a new code.");
			return false;
		}
		// Lưu referral giữa người giới thiệu và người được mời
		saveReferral(getPlayerById(referrerId), player);
		player.sendMessage("Referral code successfully used.");
		return true;
	}
	
	public void showReferrerRewards(Player player)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/referralSystem/referrer_rewards.html");
		if (html == null)
		{
			player.sendMessage("Unable to load referrer rewards page.");
			return;
		}
		// Tạo tập hợp để lưu các phần thưởng đã thêm
		Set<String> addedRewards = new HashSet<>();
		List<ItemHolder> rewards = getReferrerRewards(player); // Lấy phần thưởng cho người giới thiệu
		String rewardHtml = buildRewardText(rewards, addedRewards); // Xây dựng HTML hiển thị phần thưởng và truyền addedRewards
		html = html.replace("%referrer_rewards%", rewardHtml);
		CommunityBoardHandler.separateAndSend(html, player); // Gửi HTML tới người chơi
	}
	
	public void showReferredRewards(Player player)
	{
		String html = HtmCache.getInstance().getHtm(player, "data/html/CommunityBoard/Custom/referralSystem/referred_rewards.html");
		if (html == null)
		{
			player.sendMessage("Unable to load referred rewards page.");
			return;
		}
		// Tạo tập hợp để lưu các phần thưởng đã thêm
		Set<String> addedRewards = new HashSet<>();
		List<ItemHolder> rewards = getReferredRewards(player); // Lấy phần thưởng cho người được mời
		String rewardHtml = buildRewardText(rewards, addedRewards); // Xây dựng HTML hiển thị phần thưởng và truyền addedRewards
		html = html.replace("%referred_rewards%", rewardHtml);
		CommunityBoardHandler.separateAndSend(html, player); // Gửi HTML tới người chơi
	}
	
	public void claimReward(Player player, String condition)
	{
		// Điều kiện nhận thưởng (level, pvp)
		int value = 0;
		String[] splitCondition = condition.split("_");
		if (splitCondition.length == 1)
		{
			value = Integer.parseInt(condition); // Ví dụ: đạt cấp độ hoặc PvP
		}
		else if (splitCondition[0].equals("pvp"))
		{
			value = Integer.parseInt(splitCondition[1]);
		}
		// Lấy danh sách phần thưởng dựa trên điều kiện
		List<ItemHolder> rewards = ReferralRewardData.getInstance().getRewardsByCondition(condition, value);
		if (rewards != null && !rewards.isEmpty())
		{
			giveReward(player, rewards); // Trao phần thưởng cho người chơi
			player.sendMessage("You have successfully claimed your reward for: " + condition);
		}
		else
		{
			player.sendMessage("No reward available for the given condition.");
		}
	}
	
	public List<ItemHolder> getReferredRewards(Player player)
	{
		// Lấy phần thưởng dựa trên cấp độ hiện tại của người được mời
		List<ItemHolder> rewards = ReferralRewardData.getInstance().getRewardsByCondition("level", player.getLevel());
		// Kiểm tra nếu người chơi cũng có thể nhận phần thưởng dựa trên PvP
		int pvpKills = player.getPvpKills();
		List<ItemHolder> pvpRewards = ReferralRewardData.getInstance().getRewardsByCondition("pvp", pvpKills);
		// Nếu có phần thưởng dựa trên PvP, kết hợp chúng lại
		if (pvpRewards != null && !pvpRewards.isEmpty())
		{
			rewards.addAll(pvpRewards);
		}
		return rewards; // Trả về danh sách phần thưởng
	}
	
	public List<ItemHolder> getReferrerRewards(Player player)
	{
		int referrerId = getReferrerId(player); // Lấy ID người giới thiệu của người chơi hiện tại
		if (referrerId == 0)
		{
			return new ArrayList<>(); // Nếu không có người giới thiệu, trả về danh sách rỗng
		}
		// Lấy đối tượng Player của người giới thiệu
		Player referrer = getPlayerById(referrerId);
		if (referrer == null)
		{
			return new ArrayList<>(); // Nếu người giới thiệu không tồn tại trong hệ thống, trả về danh sách rỗng
		}
		// Lấy phần thưởng dựa trên cấp độ và PvP của người được mời (player)
		List<ItemHolder> referrerRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_level", player.getLevel());
		List<ItemHolder> pvpRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_pvp", player.getPvpKills());
		if (pvpRewards != null && !pvpRewards.isEmpty())
		{
			referrerRewards.addAll(pvpRewards); // Thêm phần thưởng dựa trên PvP vào danh sách
		}
		return referrerRewards;
	}
	
	public void showInvitedPlayerActivity(Player referrer, int invitedPlayerId)
	{
		// Kiểm tra xem người chơi có online không
		Player invitedPlayer = getPlayerById(invitedPlayerId);
		PlayerData invitedPlayerData = null;
		// Nếu người chơi offline, lấy thông tin từ cơ sở dữ liệu
		if (invitedPlayer == null)
		{
			invitedPlayerData = getOfflinePlayerData(invitedPlayerId);
			if (invitedPlayerData == null)
			{
				referrer.sendMessage("Could not load the player activity page.");
				return;
			}
		}
		String html = HtmCache.getInstance().getHtm(referrer, "data/html/CommunityBoard/Custom/referralSystem/player_activity.html");
		if (html == null)
		{
			referrer.sendMessage("Could not load the player activity page.");
			return;
		}
		// Nếu người chơi online, lấy thông tin từ đối tượng Player
		String invitedPlayerName = invitedPlayer != null ? invitedPlayer.getName() : invitedPlayerData.getName();
		int invitedPlayerLevel = invitedPlayer != null ? invitedPlayer.getLevel() : invitedPlayerData.getLevel();
		int invitedPlayerPvpKills = invitedPlayer != null ? invitedPlayer.getPvpKills() : invitedPlayerData.getPvpKills();
		// Thay thế các giá trị trong HTML
		html = html.replace("%invited_player_name%", invitedPlayerName);
		html = html.replace("%invited_player_level%", String.valueOf(invitedPlayerLevel));
		html = html.replace("%invited_player_pvp%", String.valueOf(invitedPlayerPvpKills));
		html = html.replace("%invited_player_id%", String.valueOf(invitedPlayerId));
		// Danh sách phần thưởng đã thêm, tránh trùng lặp
		Set<String> addedRewards = new HashSet<>();
		// Xây dựng danh sách phần thưởng cho level
		List<ItemHolder> levelRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_level", invitedPlayerLevel);
		StringBuilder levelRewardText = new StringBuilder();
		levelRewardText.append("<br><font color=\"LEVEL\">Rewards for Level:</font><br>");
		levelRewardText.append(buildRewardText(levelRewards, addedRewards));
		// Xây dựng danh sách phần thưởng cho PvP
		List<ItemHolder> pvpRewards = ReferralRewardData.getInstance().getRewardsByCondition("referrer_pvp", invitedPlayerPvpKills);
		StringBuilder pvpRewardText = new StringBuilder();
		pvpRewardText.append("<br><font color=\"LEVEL\">Rewards for PvP Kills:</font><br>");
		pvpRewardText.append(buildRewardText(pvpRewards, addedRewards));
		// Kết hợp hiển thị các phần thưởng theo từng loại
		StringBuilder playerRewardsHtml = new StringBuilder();
		if (!levelRewards.isEmpty())
		{
			playerRewardsHtml.append(levelRewardText);
		}
		if (!pvpRewards.isEmpty())
		{
			playerRewardsHtml.append(pvpRewardText);
		}
		// Thay thế %player_rewards% với các phần thưởng đã tính toán
		html = html.replace("%player_rewards%", playerRewardsHtml.toString());
		// Gửi HTML về cho người chơi
		CommunityBoardHandler.separateAndSend(html, referrer);
	}
	
	public List<Integer> getInvitedPlayerIds(Player referrer)
	{
		List<Integer> invitedPlayerIds = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT referred_id FROM referrals WHERE referrer_id = ?"))
		{
			ps.setInt(1, referrer.getObjectId()); // Lấy ID của người giới thiệu
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					invitedPlayerIds.add(rs.getInt("referred_id")); // Thêm ID người được mời vào danh sách
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return invitedPlayerIds; // Trả về danh sách ID người được mời
	}
	
	// Trả về Player nếu người chơi online, ngược lại trả về null
	private Player getOnlinePlayer(int playerId)
	{
		return World.getInstance().getPlayer(playerId);
	}
	
	// Trả về PlayerData nếu người chơi offline, ngược lại trả về null
	private PlayerData getOfflinePlayerData(int playerId)
	{
		PlayerData playerData = null;
		try (Connection con = DatabaseFactory.getConnection())
		{
			PreparedStatement ps = con.prepareStatement("SELECT char_name, level, pvpkills FROM characters WHERE charId = ?");
			ps.setInt(1, playerId);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					playerData = new PlayerData();
					playerData.setName(rs.getString("char_name"));
					playerData.setLevel(rs.getInt("level"));
					playerData.setPvpKills(rs.getInt("pvpkills"));
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return playerData;
	}
	
	public void cancelReferral(Player player)
	{
		int referrerId = getReferrerId(player); // Lấy ID của người giới thiệu hiện tại
		if (referrerId == 0)
		{
			player.sendMessage("You don't have a referrer to cancel.");
			return;
		}
		Player referrer = getOnlinePlayer(referrerId); // Lấy đối tượng Player của người giới thiệu (nếu người giới thiệu online)
		String referrerName = getReferrerName(player); // Lấy tên người giới thiệu
		// Xóa mã referral từ cơ sở dữ liệu
		try (Connection con = DatabaseFactory.getConnection())
		{
			// Xóa thông tin referral của người chơi
			PreparedStatement psDelete = con.prepareStatement("DELETE FROM referrals WHERE referred_id = ?");
			psDelete.setInt(1, player.getObjectId());
			psDelete.executeUpdate();
			// Xóa mã referral của người chơi khỏi bộ nhớ cache
			referralCodeCache.remove(player.getObjectId());
			player.getVariables().remove("referral_code");
			player.getVariables().remove("hasReferralCode");
			player.getVariables().storeMe(); // Lưu lại dữ liệu người chơi
			// Thông báo cho người được giới thiệu
			player.sendMessage(player.getName() + " has removed the referral relationship with you");
			player.sendPacket(new CreatureSay(player, ChatType.WHISPER, referrer.getName(), referrer.getName() + " has removed the referral relationship with you"));
			player.sendPacket(new ExShowScreenMessage(referrerName + " has removed the referral relationship with you", ExShowScreenMessage.TOP_CENTER, 5000, 1, true, true));
			// Thông báo cho người giới thiệu nếu người đó đang online
			if (referrer != null)
			{
				referrer.sendMessage("You have deleted the referral relationship with " + player.getName());
				referrer.sendPacket(new CreatureSay(referrer, ChatType.WHISPER, player.getName(), "You have deleted the referral relationship with you "));
				referrer.sendPacket(new ExShowScreenMessage("You have deleted the referral relationship with " + player.getName(), ExShowScreenMessage.TOP_CENTER, 5000, 1, true, true));
				ReferralManager.getInstance().showReferralPageInCommunityBoard(referrer, 1);
			}
		}
		catch (Exception e)
		{
			player.sendMessage("Failed to cancel referral connection.");
			e.printStackTrace();
		}
	}
	
	public String getReferrerName(Player player)
	{
		String referrerName = "N/A"; // Giá trị mặc định nếu không tìm thấy tên
		int referrerId = getReferrerId(player);
		if (referrerId == 0)
		{
			return referrerName; // Trả về nếu không có người giới thiệu
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT char_name FROM characters WHERE charId = ?"))
		{
			ps.setInt(1, referrerId);
			try (ResultSet rs = ps.executeQuery())
			{
				if (rs.next())
				{
					referrerName = rs.getString("char_name"); // Lấy tên người giới thiệu
				}
			}
		}
		catch (Exception e)
		{
			e.printStackTrace();
		}
		return referrerName;
	}
	
	public static final ReferralManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final ReferralManager INSTANCE = new ReferralManager();
	}
}
