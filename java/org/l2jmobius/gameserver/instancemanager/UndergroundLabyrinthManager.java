package org.l2jmobius.gameserver.instancemanager;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.gameserver.data.xml.SkillData;
import org.l2jmobius.gameserver.enums.SkillFinishType;
import org.l2jmobius.gameserver.model.Location;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.skill.Skill;
import org.l2jmobius.gameserver.model.zone.ZoneId;
import org.l2jmobius.gameserver.network.serverpackets.ExShowScreenMessage;

public class UndergroundLabyrinthManager
{
	// Const
	public static final int				PK_FOR_PRISON			= 10;
	public static final int				PK_AFTER_PRISON			= 0;
	public static final int				PK_FOR_WARNING			= 9;
	// Locations
	private static final Location		TELEPORT_LOC			= new Location(-213473, 244899, 2017);
	// Thêm vị trí để thả người chơi sau khi hoàn thành hình phạt.
	private static final Location		TELEPORT_OUT_LOC		= new Location(82698, 148638, -3459);			// The Center Giran
	// Skills
	private static final Skill			PRISONER_UNIFORM_SKILL	= SkillData.getInstance().getSkill(54241, 1);
	// Thời gian người chơi phải ở trong vùng (1 giờ = 3600 giây)
	private static final long			PRISON_TIME				= 40 * 60;										// 40 mins
	// Map lưu thời gian đếm ngược cho từng người chơi
	private Map<Player, LabyrinthClock>	playerClocks			= new ConcurrentHashMap<>();
	private Map<Player, Boolean>		playerReleasedStatus	= new ConcurrentHashMap<>();
	
	protected UndergroundLabyrinthManager()
	{}
	
	// Called from setReputation
	public void onPkReached(Player player)
	{
		player.sendMessage("Your pk has reached " + PK_FOR_PRISON + " you will be transported to the Underground Labyrinth");
		PRISONER_UNIFORM_SKILL.applyEffects(player, player); // Apply here, in case player escapes before zone's onEnter triggers
		teleportPlayerToLabyrinth(player);
		// Bắt đầu bộ đếm thời gian cho người chơi
		LabyrinthClock clock = new LabyrinthClock(player, (int) PRISON_TIME);
		playerClocks.put(player, clock);
		clock.startClock();
	}
	
	private void verifyPlayerInZone(Player player)
	{
		// Nếu người chơi vừa được thả ra, không teleport họ quay lại
		if (Boolean.TRUE.equals(playerReleasedStatus.get(player)))
		{
			return;
		}
		if (!player.isInsideZone(ZoneId.UNDERGROUND_LABYRINTH))
		{
			teleportPlayerToLabyrinth(player);
			// ThreadPool.schedule(() -> verifyPlayerInZone(player), 10000);
		}
	}
	
	private void teleportPlayerToLabyrinth(Player player)
	{
		player.teleToLocation(TELEPORT_LOC);
		if (player.isDead())
		{
			player.doRevive(100);
		}
		ThreadPool.schedule(() -> verifyPlayerInZone(player), 10000);
	}
	
	// Khi hết giờ, thả người chơi ra khỏi ngục
	public void releasePlayerFromLabyrinth(Player player)
	{
		if (player != null && player.isOnline())
		{
			player.sendMessage("Your time in the Underground Labyrinth has ended. You are being released.");
			player.teleToLocation(TELEPORT_OUT_LOC);
			// Reset tên người chơi trở lại tên gốc
			player.getAppearance().setVisibleName(null);
			// Xóa hiệu ứng skill "PVP"
			if (player.isAffectedBySkill(54241)) // Assuming skill ID 54241 is "PVP"
			{
				player.getEffectList().stopSkillEffects(SkillFinishType.REMOVED, PRISONER_UNIFORM_SKILL); // Loại bỏ skill nếu nó còn tồn tại
				player.sendMessage("The effect of Underground Labyrinth has been removed.");
			}
			int currentPkKills = player.getPkKills();
			if (currentPkKills > 0)
			{
				player.setPkKills(currentPkKills - 1);
			}
			if (player.getReputation() < 0)
			{
				player.setReputation(0);
			}
			// Xóa bộ đếm giờ
			LabyrinthClock clock = playerClocks.remove(player);
			if (clock != null)
			{
				clock.stopClock();
			}
			// Đặt cờ trạng thái là người chơi đã được thả ra
			playerReleasedStatus.put(player, true);
			// Xóa cờ sau một khoảng thời gian để tránh teleport lại
			ThreadPool.schedule(() -> playerReleasedStatus.remove(player), 60000L); // Xóa sau 1 phút
		}
	}
	// Called by UndergroundLabyrinthZone
	// public void onPlayerEnterLabyrinth(Player player)
	// {
	// if (player.getPkKills() <= PK_FOR_PRISON)
	// {
	// PRISONER_UNIFORM_SKILL.applyEffects(player, player);
	// }
	// }
	// Called by UndergroundLabyrinthZone
	// public void onPlayerExitLabyrinth(Player player)
	// {
	// if (player.isAffected(EffectFlag.UNDERGROUND_LABYRINTH) && player.isOnline() && player.getClient() != null && !player.getClient().isDetached())
	// {
	// player.teleToLocation(TELEPORT_OUT_LOC);
	// if (player.isAffectedBySkill(54241)) // Assuming skill ID 54241 is "UNDERGROUND_LABYRINTH"
	// {
	// player.getEffectList().stopSkillEffects(SkillFinishType.SILENT, 54241); // Loại bỏ skill nếu nó còn tồn tại
	// player.sendMessage("The effect of Underground Labyrinth has been removed.");
	// }
	// }
	// }
	
	public class LabyrinthClock implements Runnable
	{
		private final Player		player;
		private int					timeRemaining;
		private boolean				announcesCountdown;
		private ScheduledFuture<?>	task;
		
		public LabyrinthClock(Player player, int totalTime)
		{
			this.player = player;
			this.timeRemaining = totalTime;
			this.announcesCountdown = true;
			this.task = null;
		}
		
		@Override
		public void run()
		{
			try
			{
				if (announcesCountdown)
				{
					// Hiển thị thời gian còn lại cho người chơi trên màn hình
					int minutes = timeRemaining / 60;
					int seconds = timeRemaining % 60;
					String timeMessage = String.format("Time Jailed: %02d:%02d", minutes, seconds);
					ExShowScreenMessage screenMessage = new ExShowScreenMessage(timeMessage, ExShowScreenMessage.TOP_CENTER, 1000);
					player.sendPacket(screenMessage);
					// Đếm ngược
					timeRemaining--;
					if (timeRemaining <= 0)
					{
						// Hết giờ
						stopClock();
						releasePlayerFromLabyrinth(player);
						return;
					}
					// Tiếp tục chạy bộ đếm ngược mỗi giây
					task = ThreadPool.schedule(this, 1000L);
				}
			}
			catch (Exception e)
			{
				e.printStackTrace();
			}
		}
		
		public void stopClock()
		{
			if (task != null)
			{
				task.cancel(false);
			}
		}
		
		public void startClock()
		{
			// Bắt đầu đếm ngược từ thời gian đã định
			task = ThreadPool.schedule(this, 1000L);
		}
		
		public int getTimeRemaining()
		{
			return timeRemaining;
		}
	}
	
	public static UndergroundLabyrinthManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		protected static final UndergroundLabyrinthManager INSTANCE = new UndergroundLabyrinthManager();
	}
}
