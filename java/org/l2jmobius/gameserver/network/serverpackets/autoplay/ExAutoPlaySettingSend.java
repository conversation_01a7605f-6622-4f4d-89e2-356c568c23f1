/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.autoplay;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.clientpackets.autoplay.ExAutoPlaySetting.NextTargetMode;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExAutoPlaySettingSend extends ServerPacket
{
	private final int				_options;
	private final boolean			_active;
	private final bool<PERSON>			_pickUp;
	private final NextTargetMode	_nextTargetMode;
	private final boolean			_longRange;
	private final int				_potionPercent;
	private final boolean			_respectfulHunting;
	
	public ExAutoPlaySettingSend(int options, boolean active, boolean pickUp, NextTargetMode nextTargetMode, boolean longRange, int potionPercent, boolean respectfulHunting)
	{
		_options = options;
		_active = active;
		_pickUp = pickUp;
		_nextTargetMode = nextTargetMode;
		_longRange = longRange;
		_potionPercent = potionPercent;
		_respectfulHunting = respectfulHunting;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_AUTOPLAY_SETTING.writeId(this, buffer);
		buffer.writeShort(_options);
		buffer.writeByte(_active ? 1 : 0);
		buffer.writeByte(_pickUp ? 1 : 0);
		buffer.writeShort(_nextTargetMode.ordinal());
		buffer.writeByte(_longRange ? 0x00 : 0x01);
		buffer.writeInt(_potionPercent);
		buffer.writeByte(_respectfulHunting ? 1 : 0);
	}
}
