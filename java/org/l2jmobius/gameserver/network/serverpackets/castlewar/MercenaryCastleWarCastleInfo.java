/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.castlewar;

import java.util.Calendar;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.enums.TaxType;
import org.l2jmobius.gameserver.instancemanager.CastleManager;
import org.l2jmobius.gameserver.model.clan.Clan;
import org.l2jmobius.gameserver.model.siege.Castle;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR> Mobius
 */
public class MercenaryCastleWarCastleInfo extends ServerPacket
{
	private final Castle _castle;
	
	public MercenaryCastleWarCastleInfo(int castleId)
	{
		_castle = CastleManager.getInstance().getCastleById(castleId);
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_MERCENARY_CASTLEWAR_CASTLE_INFO.writeId(this, buffer);
		if (_castle == null)
		{
			buffer.writeInt(_castle.getResidenceId());
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeSizedString("");
			buffer.writeSizedString("");
			buffer.writeInt(0);
			buffer.writeLong(0);
			buffer.writeLong(0);
			buffer.writeInt(0);
			return;
		}
		final Clan clan = _castle.getOwner();
		buffer.writeInt(_castle.getResidenceId());
		buffer.writeInt(clan != null ? clan.getCrestId() : 0); // CastleOwnerPledgeSID
		buffer.writeInt(clan != null ? clan.getCrestLargeId() : 0); // CastleOwnerPledgeCrestDBID
		buffer.writeSizedString(clan != null ? clan.getName() : "-"); // CastleOwnerPledgeName
		buffer.writeSizedString(clan != null ? clan.getLeaderName() : "-"); // CastleOwnerPledgeMasterName
		buffer.writeInt(_castle.getTaxPercent(TaxType.BUY)); // CastleTaxRate
		final long treasury = _castle.getTreasury();
		buffer.writeLong((long) (treasury * _castle.getTaxRate(TaxType.BUY))); // CurrentIncome
		buffer.writeLong((long) (treasury + (treasury * _castle.getTaxRate(TaxType.BUY)))); // TotalIncome
		final Calendar siegeDate = _castle.getSiegeDate();
		buffer.writeInt(siegeDate != null ? (int) (siegeDate.getTimeInMillis() / 1000) : 0); // NextSiegeTime
	}
}
