/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.castlewar;

import java.util.ArrayList;
import java.util.List;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.data.sql.ClanTable;
import org.l2jmobius.gameserver.enums.SiegeClanType;
import org.l2jmobius.gameserver.instancemanager.CastleManager;
import org.l2jmobius.gameserver.model.SiegeClan;
import org.l2jmobius.gameserver.model.clan.Clan;
import org.l2jmobius.gameserver.model.siege.Castle;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class MercenaryCastleWarCastleSiegeDefenderList extends ServerPacket
{
	private final int			_castleId;
	private final Castle		_castle;
	private final Clan			_owner;
	private final List<Clan>	_defenders			= new ArrayList<>();
	private final List<Clan>	_defendersWaiting	= new ArrayList<>();
	
	public MercenaryCastleWarCastleSiegeDefenderList(int castleId)
	{
		_castleId = castleId;
		_castle = CastleManager.getInstance().getCastleById(castleId);
		// Owner.
		_owner = _castle.getOwner();
		// Defenders.
		for (SiegeClan siegeClan : _castle.getSiege().getDefenderClans())
		{
			final Clan clan = ClanTable.getInstance().getClan(siegeClan.getClanId());
			if ((clan != null) && (clan != _castle.getOwner()))
			{
				_defenders.add(clan);
			}
		}
		// Defenders waiting.
		for (SiegeClan siegeClan : _castle.getSiege().getDefenderWaitingClans())
		{
			final Clan clan = ClanTable.getInstance().getClan(siegeClan.getClanId());
			if (clan != null)
			{
				_defendersWaiting.add(clan);
			}
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_DEFENDER_LIST.writeId(this, buffer);
		buffer.writeInt(_castleId);
		buffer.writeInt(0);
		buffer.writeInt(1);
		buffer.writeInt(0);
		if (_castle == null)
		{
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
		else
		{
			final int size = (_owner != null ? 1 : 0) + _defenders.size() + _defendersWaiting.size();
			buffer.writeInt(size);
			buffer.writeInt(size);
			// Owners.
			if (_owner != null)
			{
				buffer.writeInt(_owner.getId());
				buffer.writeString(_owner.getName());
				buffer.writeString(_owner.getLeaderName());
				buffer.writeInt(_owner.getCrestId());
				buffer.writeInt(0); // time (seconds)
				buffer.writeInt(SiegeClanType.OWNER.ordinal());
				buffer.writeInt(_owner.getAllyId());
				buffer.writeString(_owner.getAllyName());
				buffer.writeString(""); // AllyLeaderName
				buffer.writeInt(_owner.getAllyCrestId());
			}
			// Defenders.
			for (Clan defender : _defenders)
			{
				buffer.writeInt(defender.getId());
				buffer.writeString(defender.getName());
				buffer.writeString(defender.getLeaderName());
				buffer.writeInt(defender.getCrestId());
				buffer.writeInt(0); // time (seconds)
				buffer.writeInt(SiegeClanType.DEFENDER.ordinal());
				buffer.writeInt(defender.getAllyId());
				buffer.writeString(defender.getAllyName());
				buffer.writeString(""); // AllyLeaderName
				buffer.writeInt(defender.getAllyCrestId());
			}
			// Defenders waiting.
			for (Clan defender : _defendersWaiting)
			{
				buffer.writeInt(defender.getId());
				buffer.writeString(defender.getName());
				buffer.writeString(defender.getLeaderName());
				buffer.writeInt(defender.getCrestId());
				buffer.writeInt(0); // time (seconds)
				buffer.writeInt(SiegeClanType.DEFENDER_PENDING.ordinal());
				buffer.writeInt(defender.getAllyId());
				buffer.writeString(defender.getAllyName());
				buffer.writeString(""); // AllyLeaderName
				buffer.writeInt(defender.getAllyCrestId());
			}
		}
	}
}
