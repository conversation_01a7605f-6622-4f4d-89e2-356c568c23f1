/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.castlewar;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.instancemanager.CastleManager;
import org.l2jmobius.gameserver.instancemanager.SiegeManager;
import org.l2jmobius.gameserver.model.siege.Castle;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class MercenaryCastleWarCastleSiegeHudInfo extends ServerPacket
{
	private final Castle _castle;
	
	public MercenaryCastleWarCastleSiegeHudInfo(int castleId)
	{
		_castle = CastleManager.getInstance().getCastleById(castleId);
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		if (_castle == null)
		{
			return;
		}
		ServerPackets.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_HUD_INFO.writeId(this, buffer);
		buffer.writeInt(_castle.getResidenceId());
		if (_castle.getSiege().isInProgress())
		{
			buffer.writeInt(1);
			buffer.writeInt(0);
			buffer.writeInt((int) (((_castle.getSiegeDate().getTimeInMillis() + (SiegeManager.getInstance().getSiegeLength() * 60000)) - System.currentTimeMillis()) / 1000));
		}
		else
		{
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeInt((int) ((_castle.getSiegeDate().getTimeInMillis() - System.currentTimeMillis()) / 1000));
		}
	}
}
