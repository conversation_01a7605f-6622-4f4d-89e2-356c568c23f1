/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.castlewar;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.instancemanager.CastleManager;
import org.l2jmobius.gameserver.model.clan.Clan;
import org.l2jmobius.gameserver.model.siege.Castle;
import org.l2jmobius.gameserver.model.siege.Siege;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class MercenaryCastleWarCastleSiegeInfo extends ServerPacket
{
	private final int		_castleId;
	private final Castle	_castle;
	
	public MercenaryCastleWarCastleSiegeInfo(int castleId)
	{
		_castleId = castleId;
		_castle = CastleManager.getInstance().getCastleById(castleId);
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_MERCENARY_CASTLEWAR_CASTLE_SIEGE_INFO.writeId(this, buffer);
		buffer.writeInt(_castleId);
		if (_castle == null)
		{
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeSizedString("-");
			buffer.writeSizedString("-");
			buffer.writeInt(0);
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
		else
		{
			buffer.writeInt(0); // seconds?
			buffer.writeInt(0); // crest?
			final Clan clan = _castle.getOwner();
			buffer.writeSizedString(clan != null ? clan.getName() : "-");
			buffer.writeSizedString(clan != null ? clan.getLeaderName() : "-");
			buffer.writeInt(0); // crest?
			final Siege siege = _castle.getSiege();
			buffer.writeInt(siege.getAttackerClans().size());
			buffer.writeInt(siege.getDefenderClans().size() + siege.getDefenderWaitingClans().size());
		}
	}
}
