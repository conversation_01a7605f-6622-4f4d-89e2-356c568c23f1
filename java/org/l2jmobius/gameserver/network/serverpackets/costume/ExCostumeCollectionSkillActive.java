/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.costume;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.costumes.CostumeCollectionData;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExCostumeCollectionSkillActive extends ServerPacket
{
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_COSTUME_COLLECTION_SKILL_ACTIVE.writeId(this, buffer);
		final CostumeCollectionData collection = client.getPlayer().getActiveCostumeCollection();
		buffer.writeInt(collection.getId());
		buffer.writeInt(collection.getReuseTime());
	}
}