/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.costume;

import java.util.Collection;
import java.util.Set;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExCostumeEvolution extends ServerPacket
{
	private boolean				_success;
	private Collection<Costume>	_targetCostumes;
	private Costume				_resultCostume;
	
	private ExCostumeEvolution()
	{}
	
	public static ExCostumeEvolution failed()
	{
		return new ExCostumeEvolution();
	}
	
	public static ExCostumeEvolution success(Set<Costume> costume, Costume resultCostume)
	{
		final ExCostumeEvolution packet = new ExCostumeEvolution();
		packet._success = true;
		packet._targetCostumes = costume;
		packet._resultCostume = resultCostume;
		return packet;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_COSTUME_EVOLUTION.writeId(this, buffer);
		buffer.writeByte(_success);
		if (_targetCostumes != null)
		{
			buffer.writeInt(_targetCostumes.size());
			for (Costume targetCostume : _targetCostumes)
			{
				buffer.writeInt(targetCostume.getId());
				buffer.writeLong(targetCostume.getAmount());
			}
		}
		else
		{
			buffer.writeInt(0); // Handle the case where targetCostumes is null
		}
		if (_resultCostume != null)
		{
			buffer.writeInt(1);
			buffer.writeInt(_resultCostume.getId());
			buffer.writeLong(_resultCostume.getAmount());
		}
		else
		{
			buffer.writeInt(0);
		}
	}
}