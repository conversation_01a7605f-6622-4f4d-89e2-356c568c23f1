/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.costume;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExCostumeExtract extends ServerPacket
{
	private int		_costumeId;
	private boolean	_success;
	private long	_amount;
	private int		_extractedItem;
	private long	_totalAmount;
	
	private ExCostumeExtract()
	{}
	
	public static ExCostumeExtract failed(int costumeId)
	{
		final ExCostumeExtract packet = new ExCostumeExtract();
		packet._costumeId = costumeId;
		return packet;
	}
	
	public static ExCostumeExtract success(Costume costume, int extractItem, long amount)
	{
		final ExCostumeExtract packet = new ExCostumeExtract();
		packet._costumeId = costume.getId();
		packet._success = true;
		packet._extractedItem = extractItem;
		packet._amount = amount;
		packet._totalAmount = costume.getAmount();
		return packet;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_COSTUME_EXTRACT.writeId(this, buffer);
		buffer.writeByte(_success);
		buffer.writeInt(_costumeId);
		buffer.writeLong(_amount);
		buffer.writeInt(_extractedItem);
		buffer.writeLong(_amount);
		buffer.writeLong(_totalAmount);
	}
}