/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.costume;

import java.util.List;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.costumes.CostumeShortcutInfo;
import org.l2jmobius.gameserver.model.costumes.CostumeShortcutManager;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExCostumeShortcutList extends ServerPacket
{
	private final boolean					_result;
	private final List<CostumeShortcutInfo>	_list;
	
	public ExCostumeShortcutList(int playerId)
	{
		_result = true; // Assuming result is always true when sending shortcuts.
		_list = CostumeShortcutManager.loadPlayerShortcuts(playerId);
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_COSTUME_SHORTCUT_LIST.writeId(this, buffer);
		buffer.writeByte(_result ? 1 : 0);
		buffer.writeInt(_list.size());
		for (CostumeShortcutInfo info : _list)
		{
			buffer.writeInt(info.getPage());
			buffer.writeInt(info.getSlot());
			buffer.writeInt(info.getCostumeId());
		}
	}
	

}