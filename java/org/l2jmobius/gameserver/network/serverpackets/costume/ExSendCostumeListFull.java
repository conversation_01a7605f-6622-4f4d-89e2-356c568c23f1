/*
 * This file is part of the L2j Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.costume;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.costumes.Costume;
import org.l2jmobius.gameserver.model.costumes.CostumeManager;
import org.l2jmobius.gameserver.model.costumes.CostumeShortcutInfo;
import org.l2jmobius.gameserver.model.costumes.CostumeShortcutManager;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR>
 */
public class ExSendCostumeListFull extends ServerPacket
{
	private static final Logger LOGGER = Logger.getLogger(ExSendCostumeListFull.class.getName());
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_SEND_COSTUME_LIST_FULL.writeId(this, buffer);
		final Player player = client.getPlayer();

		if (player == null)
		{
			// Send empty data if player is null
			buffer.writeInt(0); // costume count
			buffer.writeInt(0); // shortcut count
			return;
		}

		// Force refresh player costume cache before loading
		try
		{
			CostumeManager.getInstance().processCollections(player);
		}
		catch (Exception e)
		{
			LOGGER.warning("Failed to refresh player costume cache: " + e.getMessage());
		}

		// Load costume list from database (always fresh data)
		final List<Costume> costumes = loadCostumesOptimized(player.getObjectId());
		buffer.writeInt(costumes.size());
		for (Costume costume : costumes)
		{
			buffer.writeInt(costume.getId());
			buffer.writeLong(costume.getAmount());
			buffer.writeByte(costume.isLocked() ? 1 : 0);
			buffer.writeByte(costume.checkIsNewAndChange() ? 1 : 0);
		}

		// Load shortcut list from database (always fresh data)
		final List<CostumeShortcutInfo> shortcuts = CostumeShortcutManager.loadPlayerShortcuts(player.getObjectId());
		buffer.writeInt(shortcuts.size());
		for (CostumeShortcutInfo shortcut : shortcuts)
		{
			buffer.writeInt(shortcut.getPage());
			buffer.writeInt(shortcut.getSlot());
			buffer.writeInt(shortcut.getCostumeId());
		}
	}
	
	/**
	 * Optimized method to load costumes - uses player cache if available
	 */
	private List<Costume> loadCostumesOptimized(int playerId)
	{
		final List<Costume> costumeList = new ArrayList<>();
		try (Connection con = DatabaseFactory.getConnection();
			 PreparedStatement ps = con.prepareStatement("SELECT id, amount, locked FROM character_costumes WHERE player_id = ? AND amount > 0 ORDER BY id"))
		{
			ps.setInt(1, playerId);
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					final Costume costume = new Costume();
					costume.setId(rs.getInt("id"));
					costume.setAmount(rs.getLong("amount"));
					costume.setLocked(rs.getBoolean("locked"));
					costume.setNew(false); // Loaded from DB, so not new
					costumeList.add(costume);
				}
			}
		}
		catch (SQLException e)
		{
			LOGGER.warning("Failed to load costumes for player ID: " + playerId + " - " + e.getMessage());
		}
		return costumeList;
	}
	

}