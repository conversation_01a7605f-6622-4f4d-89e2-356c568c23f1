/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.ranking;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.LinkedList;
import java.util.List;

import org.l2jmobius.Config;
import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.data.sql.ClanTable;
import org.l2jmobius.gameserver.instancemanager.RankManager;
import org.l2jmobius.gameserver.model.olympiad.Hero;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.PacketLogger;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR> Mobius
 */
public class ExOlympiadHeroAndLegendInfo extends ServerPacket
{
	// TODO: Move query and store data at RankManager.
	private static final String		GET_HEROES	= "SELECT characters.charId, characters.char_name, characters.race, characters.sex, characters.base_class, characters.level, characters.clanid, olympiad_nobles_eom.competitions_won, olympiad_nobles_eom.competitions_lost, olympiad_nobles_eom.olympiad_points, heroes.legend_count, heroes.count FROM heroes, characters, olympiad_nobles_eom WHERE characters.charId = heroes.charId AND characters.charId = olympiad_nobles_eom.charId AND heroes.played = 1 ORDER BY olympiad_nobles_eom.olympiad_points DESC, characters.base_class ASC LIMIT " + RankManager.PLAYER_LIMIT;
	private final List<HeroInfo>	_heroes		= new LinkedList<>();
	
	public ExOlympiadHeroAndLegendInfo()
	{
		if (Hero.getInstance().getHeroes().isEmpty())
		{
			return;
		}
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(GET_HEROES); ResultSet rset = statement.executeQuery())
		{
			boolean isFirstHero = true;
			while (rset.next())
			{
				final String charName = rset.getString("char_name");
				final int clanId = rset.getInt("clanid");
				final String clanName = (clanId > 0) ? ClanTable.getInstance().getClan(clanId).getName() : "";
				final int race = rset.getInt("race");
				final boolean isMale = rset.getInt("sex") != 1;
				final int baseClass = rset.getInt("base_class");
				final int level = rset.getInt("level");
				final int legendCount = rset.getInt("legend_count");
				final int competitionsWon = rset.getInt("competitions_won");
				final int competitionsLost = rset.getInt("competitions_lost");
				final int olympiadPoints = rset.getInt("olympiad_points");
				final int clanLevel = (clanId > 0) ? ClanTable.getInstance().getClan(clanId).getLevel() : 0;
				final boolean isTopHero = isFirstHero;
				_heroes.add(new HeroInfo(charName, clanName, Config.SERVER_ID, race, isMale, baseClass, level, legendCount, competitionsWon, competitionsLost, olympiadPoints, clanLevel, isTopHero));
				isFirstHero = false;
			}
		}
		catch (SQLException e)
		{
			PacketLogger.warning("Hero and Legend Info: Could not load data: " + e.getMessage());
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_OLYMPIAD_HERO_AND_LEGEND_INFO.writeId(this, buffer);
		if (_heroes.isEmpty())
		{
			return;
		}
		for (HeroInfo hero : _heroes)
		{
			if (hero.isTopHero)
			{
				buffer.writeByte(1); // ?? shows 78 on JP
				buffer.writeByte(1); // ?? shows 0 on JP
			}
			buffer.writeSizedString(hero.charName);
			buffer.writeSizedString(hero.clanName);
			buffer.writeInt(hero.serverId);
			buffer.writeInt(hero.race);
			buffer.writeInt(hero.isMale ? 1 : 0);
			buffer.writeInt(hero.baseClass);
			buffer.writeInt(hero.level);
			buffer.writeInt(hero.legendCount);
			buffer.writeInt(hero.competitionsWon);
			buffer.writeInt(hero.competitionsLost);
			buffer.writeInt(hero.olympiadPoints);
			buffer.writeInt(hero.clanLevel);
			if (!hero.isTopHero)
			{
				buffer.writeInt(_heroes.size() - 1); // Write the number of additional heroes.
			}
		}
	}
	
	private class HeroInfo
	{
		String	charName;
		String	clanName;
		int		serverId;
		int		race;
		boolean	isMale;
		int		baseClass;
		int		level;
		int		legendCount;
		int		competitionsWon;
		int		competitionsLost;
		int		olympiadPoints;
		int		clanLevel;
		boolean	isTopHero;
		
		HeroInfo(String charName, String clanName, int serverId, int race, boolean isMale, int baseClass, int level, int legendCount, int competitionsWon, int competitionsLost, int olympiadPoints, int clanLevel, boolean isTopHero)
		{
			this.charName = charName;
			this.clanName = clanName;
			this.serverId = serverId;
			this.race = race;
			this.isMale = isMale;
			this.baseClass = baseClass;
			this.level = level;
			this.legendCount = legendCount;
			this.competitionsWon = competitionsWon;
			this.competitionsLost = competitionsLost;
			this.olympiadPoints = olympiadPoints;
			this.clanLevel = clanLevel;
			this.isTopHero = isTopHero;
		}
	}
}
