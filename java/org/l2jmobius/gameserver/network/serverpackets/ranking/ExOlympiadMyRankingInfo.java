/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.ranking;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import org.l2jmobius.commons.database.DatabaseFactory;
import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.instancemanager.RankManager;
import org.l2jmobius.gameserver.model.StatSet;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.olympiad.Hero;
import org.l2jmobius.gameserver.model.olympiad.Olympiad;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.PacketLogger;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR> Mobius
 */
public class ExOlympiadMyRankingInfo extends ServerPacket
{
	// TODO: Move query and store data at RankManager.
	private static final String	GET_CURRENT_CYCLE_DATA	= "SELECT charId, olympiad_points, competitions_won, competitions_lost FROM olympiad_nobles WHERE class_id = ? ORDER BY olympiad_points DESC, competitions_won DESC LIMIT " + RankManager.PLAYER_LIMIT;
	private static final String	GET_PREVIOUS_CYCLE_DATA	= "SELECT charId, olympiad_points, competitions_won, competitions_lost FROM olympiad_nobles_eom WHERE class_id = ? ORDER BY olympiad_points DESC, competitions_won DESC LIMIT " + RankManager.PLAYER_LIMIT;
	private final int			_year;
	private final int			_month;
	private final int			_cycle;
	private final int			_currentPlace;
	private final int			_currentWins;
	private final int			_currentLoses;
	private final int			_currentPoints;
	private final int			_previousPlace;
	private final int			_previousWins;
	private final int			_previousLoses;
	private final int			_previousPoints;
	private final int			_heroCount;
	private final int			_legendCount;
	
	public ExOlympiadMyRankingInfo(Player player)
	{
		// Initialize calendar for year and month.
		final Calendar calendar = new GregorianCalendar();
		calendar.setTime(new Date());
		_year = calendar.get(Calendar.YEAR);
		_month = calendar.get(Calendar.MONTH) + 1;
		_cycle = Math.max(Olympiad.getInstance().getCurrentCycle() - 1, 0);
		// Initialize current cycle variables.
		int tempCurrentPlace = 0;
		int tempCurrentWins = 0;
		int tempCurrentLoses = 0;
		int tempCurrentPoints = 0;
		// Fetch current cycle data.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(GET_CURRENT_CYCLE_DATA))
		{
			statement.setInt(1, player.getBaseClass());
			try (ResultSet rset = statement.executeQuery())
			{
				int i = 1;
				while (rset.next())
				{
					if (rset.getInt("charId") == player.getObjectId())
					{
						tempCurrentPlace = i;
						tempCurrentWins = rset.getInt("competitions_won");
						tempCurrentLoses = rset.getInt("competitions_lost");
						tempCurrentPoints = rset.getInt("olympiad_points");
					}
					i++;
				}
			}
		}
		catch (SQLException e)
		{
			PacketLogger.warning("Olympiad my ranking: Could not load current cycle data: " + e.getMessage());
		}
		// Initialize previous cycle variables.
		int tempPreviousPlace = 0;
		int tempPreviousWins = 0;
		int tempPreviousLoses = 0;
		int tempPreviousPoints = 0;
		// Fetch previous cycle data.
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement statement = con.prepareStatement(GET_PREVIOUS_CYCLE_DATA))
		{
			statement.setInt(1, player.getBaseClass());
			try (ResultSet rset = statement.executeQuery())
			{
				int i = 1;
				while (rset.next())
				{
					if (rset.getInt("charId") == player.getObjectId())
					{
						tempPreviousPlace = i;
						tempPreviousWins = rset.getInt("competitions_won");
						tempPreviousLoses = rset.getInt("competitions_lost");
						tempPreviousPoints = rset.getInt("olympiad_points");
					}
					i++;
				}
			}
		}
		catch (SQLException e)
		{
			PacketLogger.warning("Olympiad my ranking: Could not load previous cycle data: " + e.getMessage());
		}
		// Fetch hero and legend counts.
		int tempHeroCount = 0;
		int tempLegendCount = 0;
		if (Hero.getInstance().getCompleteHeroes().containsKey(player.getObjectId()))
		{
			StatSet hero = Hero.getInstance().getCompleteHeroes().get(player.getObjectId());
			tempHeroCount = hero.getInt("count", 0);
			tempLegendCount = hero.getInt("legend_count", 0);
		}
		// Assign values to final fields.
		_currentPlace = tempCurrentPlace;
		_currentWins = tempCurrentWins;
		_currentLoses = tempCurrentLoses;
		_currentPoints = tempCurrentPoints;
		_previousPlace = tempPreviousPlace;
		_previousWins = tempPreviousWins;
		_previousLoses = tempPreviousLoses;
		_previousPoints = tempPreviousPoints;
		_heroCount = tempHeroCount;
		_legendCount = tempLegendCount;
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_OLYMPIAD_MY_RANKING_INFO.writeId(this, buffer);
		buffer.writeInt(_year);
		buffer.writeInt(_month);
		buffer.writeInt(_cycle);
		buffer.writeInt(_currentPlace);
		buffer.writeInt(_currentWins);
		buffer.writeInt(_currentLoses);
		buffer.writeInt(_currentPoints);
		buffer.writeInt(_previousPlace);
		buffer.writeInt(_previousWins);
		buffer.writeInt(_previousLoses);
		buffer.writeInt(_previousPoints);
		buffer.writeInt(_heroCount);
		buffer.writeInt(_legendCount);
		buffer.writeInt(0); // Unknown
	}
}
