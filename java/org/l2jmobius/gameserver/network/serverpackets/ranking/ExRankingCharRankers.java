/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.network.serverpackets.ranking;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.l2jmobius.commons.network.WritableBuffer;
import org.l2jmobius.gameserver.instancemanager.RankManager;
import org.l2jmobius.gameserver.model.StatSet;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.clan.Clan;
import org.l2jmobius.gameserver.network.GameClient;
import org.l2jmobius.gameserver.network.ServerPackets;
import org.l2jmobius.gameserver.network.serverpackets.ServerPacket;

/**
 * <AUTHOR> Mobius
 */
public class ExRankingCharRankers extends ServerPacket
{
	private final Player			_player;
	private final int				_group;
	private final int				_scope;
	private final int				_race;
	private final List<RankInfo>	_rankInfoList	= new LinkedList<>();
	
	public ExRankingCharRankers(Player player, int group, int scope, int race)
	{
		_player = player;
		_group = group;
		_scope = scope;
		_race = race;
		final Map<Integer, StatSet> playerList = RankManager.getInstance().getRankList();
		final Map<Integer, StatSet> snapshotList = RankManager.getInstance().getSnapshotList();
		switch (_group)
		{
			case 0: // All
			{
				processAllGroup(playerList, snapshotList);
				break;
			}
			case 1: // Race
			{
				processRaceGroup(playerList, snapshotList);
				break;
			}
			case 2: // Clan
			{
				processClanGroup(playerList, snapshotList);
				break;
			}
			case 3: // Friend
			{
				processFriendGroup(playerList, snapshotList);
				break;
			}
		}
	}
	
	private void processAllGroup(Map<Integer, StatSet> playerList, Map<Integer, StatSet> snapshotList)
	{
		if (_scope == 0) // All.
		{
			final int max = Math.min(playerList.size(), 150);
			int count = 0;
			for (Integer id : playerList.keySet())
			{
				if (count >= max)
				{
					break;
				}
				final StatSet player = playerList.get(id);
				_rankInfoList.add(new RankInfo(player, id, snapshotList));
				count++;
			}
		}
		else
		{
			boolean found = false;
			for (Integer id : playerList.keySet())
			{
				final StatSet player = playerList.get(id);
				if (player.getInt("charId") == _player.getObjectId())
				{
					found = true;
					final int first = Math.max(id - 9, 1);
					final int last = Math.min(id + 10, playerList.size());
					for (int id2 = first; id2 <= last; id2++)
					{
						final StatSet plr = playerList.get(id2);
						_rankInfoList.add(new RankInfo(plr, id2, snapshotList));
					}
					break;
				}
			}
			if (!found)
			{
				_rankInfoList.add(new RankInfo()); // Empty entry if not found.
			}
		}
	}
	
	private void processRaceGroup(Map<Integer, StatSet> playerList, Map<Integer, StatSet> snapshotList)
	{
		if (_scope == 0) // All.
		{
			int max = (int) playerList.values().stream().filter(p -> p.getInt("race") == _race).count();
			max = Math.min(max, 100);
			int count = 0;
			for (Entry<Integer, StatSet> entry : playerList.entrySet())
			{
				if (count >= max)
				{
					break;
				}
				final StatSet player = entry.getValue();
				if (player.getInt("race") == _race)
				{
					_rankInfoList.add(new RankInfo(player, entry.getKey(), snapshotList));
					count++;
				}
			}
		}
		else
		{
			boolean found = false;
			int i = 1;
			for (Entry<Integer, StatSet> entry : playerList.entrySet())
			{
				final StatSet set = entry.getValue();
				if (_player.getRace().ordinal() == set.getInt("race"))
				{
					if (set.getInt("charId") == _player.getObjectId())
					{
						found = true;
						int first = Math.max(i - 9, 1);
						int last = Math.min(i + 10, playerList.size());
						for (int id = first; id <= last; id++)
						{
							final StatSet player = playerList.get(id);
							if (player != null)
							{
								_rankInfoList.add(new RankInfo(player, id, snapshotList));
							}
						}
						break;
					}
					i++;
				}
			}
			if (!found)
			{
				_rankInfoList.add(new RankInfo()); // Empty entry if not found.
			}
		}
	}
	
	private void processClanGroup(Map<Integer, StatSet> playerList, Map<Integer, StatSet> snapshotList)
	{
		final Clan clan = _player.getClan();
		if (clan != null)
		{
			for (Entry<Integer, StatSet> entry : playerList.entrySet())
			{
				final StatSet set = entry.getValue();
				if (clan.getName().equals(set.getString("clanName")))
				{
					_rankInfoList.add(new RankInfo(set, entry.getKey(), snapshotList));
					break;
				}
			}
		}
		else
		{
			_rankInfoList.add(new RankInfo()); // Empty entry if no clan.
		}
	}
	
	private void processFriendGroup(Map<Integer, StatSet> playerList, Map<Integer, StatSet> snapshotList)
	{
		final Set<Integer> friendList = new HashSet<>(_player.getFriendList());
		friendList.add(_player.getObjectId());
		for (Integer id : playerList.keySet())
		{
			final StatSet player = playerList.get(id);
			if (friendList.contains(player.getInt("charId")))
			{
				_rankInfoList.add(new RankInfo(player, id, snapshotList));
			}
		}
		if (_rankInfoList.isEmpty())
		{
			_rankInfoList.add(new RankInfo(_player)); // Default entry if no friends.
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		ServerPackets.EX_RANKING_CHAR_RANKERS.writeId(this, buffer);
		buffer.writeByte(_group);
		buffer.writeByte(_scope);
		buffer.writeInt(_race);
		buffer.writeInt(_rankInfoList.size());
		for (RankInfo rankInfo : _rankInfoList)
		{
			buffer.writeSizedString(rankInfo.name);
			buffer.writeSizedString(rankInfo.clanName);
			buffer.writeInt(rankInfo.level);
			buffer.writeInt(rankInfo.classId);
			buffer.writeInt(rankInfo.race);
			buffer.writeInt(rankInfo.serverRank);
			buffer.writeInt(rankInfo.serverRankSnapshot);
			buffer.writeInt(rankInfo.raceRankSnapshot);
		}
	}
	
	private static class RankInfo
	{
		String	name;
		String	clanName;
		int		level;
		int		classId;
		int		race;
		int		serverRank;
		int		serverRankSnapshot;
		int		raceRankSnapshot;
		
		RankInfo()
		{
			this.name = "";
			this.clanName = "";
			this.level = 0;
			this.classId = 0;
			this.race = 0;
			this.serverRank = 0;
			this.serverRankSnapshot = 0;
			this.raceRankSnapshot = 0;
		}
		
		RankInfo(StatSet player, int serverRank, Map<Integer, StatSet> snapshotList)
		{
			this.name = player.getString("name");
			this.clanName = player.getString("clanName");
			this.level = player.getInt("level");
			this.classId = player.getInt("classId");
			this.race = player.getInt("race");
			this.serverRank = serverRank;
			final StatSet snapshot = snapshotList.values().stream().filter(s -> s.getInt("charId") == player.getInt("charId")).findFirst().orElse(null);
			if (snapshot != null)
			{
				this.serverRankSnapshot = snapshot.getInt("serverRankSnapshot", 0);
				this.raceRankSnapshot = snapshot.getInt("raceRank", 0);
			}
			else
			{
				this.serverRankSnapshot = 0;
				this.raceRankSnapshot = 0;
			}
		}
		
		RankInfo(Player player)
		{
			final Clan clan = player.getClan();
			this.name = player.getName();
			this.clanName = (clan != null) ? clan.getName() : "";
			this.level = player.getStat().getBaseLevel();
			this.classId = player.getBaseClass();
			this.race = player.getRace().ordinal();
			this.serverRank = 1; // Default rank for player.
			this.serverRankSnapshot = 0;
			this.raceRankSnapshot = 0;
		}
	}
}
