/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package org.l2jmobius.gameserver.taskmanager;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.l2jmobius.commons.threads.ThreadPool;
import org.l2jmobius.gameserver.model.actor.Player;
import org.l2jmobius.gameserver.model.skill.AbnormalType;
import org.l2jmobius.gameserver.network.Disconnection;
import org.l2jmobius.gameserver.network.serverpackets.LeaveWorld;

/**
 * Task Manager to monitor players in offline play mode based on active effects.
 */
public class OfflinePlayEffectTaskManager implements Runnable
{
	private static final Map<Player, Long> PLAYERS = new ConcurrentHashMap<>();
	
	private OfflinePlayEffectTaskManager()
	{
		// Giảm thời gian kiểm tra từ 1000ms xuống 500ms
		ThreadPool.scheduleAtFixedRate(this, 500, 500);
	}
	
	@Override
	public void run()
	{
		final Iterator<Map.Entry<Player, Long>> iterator = PLAYERS.entrySet().iterator();
		while (iterator.hasNext())
		{
			final Map.Entry<Player, Long> entry = iterator.next();
			final Player player = entry.getKey();
			if (player == null || isOfflinePlayExpired(player))
			{
				// System.out.println("DEBUG: Player " + player.getName() + " đã hết thời gian Offline Play -> KICKING");
				kickPlayer(player);
				iterator.remove();
			}
		}
	}
	
	private boolean isOfflinePlayExpired(Player player)
	{
		AbnormalType[] offlinePlayTypes =
		{
			AbnormalType.OFFLINE_PLAY_3H, AbnormalType.OFFLINE_PLAY_5H, AbnormalType.OFFLINE_PLAY_10H
		};
		for (AbnormalType type : offlinePlayTypes)
		{
			if (player.getEffectList().getRemainingTimeForAbnormalType(type) > 0)
			{
				return false; // Còn hiệu ứng, chưa hết hạn
			}
		}
		return true; // Không còn hiệu ứng nào
	}
	
	private boolean hasOfflinePlayEffect(Player player)
	{
		return player.getEffectList().getRemainingTimeForAbnormalType(AbnormalType.OFFLINE_PLAY_3H) != 0 || player.getEffectList().getRemainingTimeForAbnormalType(AbnormalType.OFFLINE_PLAY_5H) != 0 || player.getEffectList().getRemainingTimeForAbnormalType(AbnormalType.OFFLINE_PLAY_10H) != 0;
	}
	
	private void kickPlayer(Player player)
	{
		if (player != null && player.isOnline())
		{ // Chỉ kick nếu player đang online
			// System.out.println("DEBUG: Kicking player " + player.getName());
			player.stopOfflinePlay();
			player.sendPacket(LeaveWorld.STATIC_PACKET);
			Disconnection.of(player).storeMe().deleteMe();
			player.logout();
		}
	}
	
	public void addPlayer(Player player)
	{
		if (player != null && hasOfflinePlayEffect(player))
		{
			PLAYERS.putIfAbsent(player, System.currentTimeMillis());
		}
	}
	
	public void removePlayer(Player player)
	{
		PLAYERS.remove(player);
	}
	
	public static OfflinePlayEffectTaskManager getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	private static class SingletonHolder
	{
		private static final OfflinePlayEffectTaskManager INSTANCE = new OfflinePlayEffectTaskManager();
	}
}
